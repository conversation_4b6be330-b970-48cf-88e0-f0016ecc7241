# Authentication in YoutubeDownloader API

## How It Works

The YoutubeDownloader API works exactly like the desktop application:

### No Authentication Required (Default)
The API works **WITHOUT any authentication** for:
- ✅ All public YouTube videos
- ✅ Most regular content
- ✅ Videos without age restrictions
- ✅ Public playlists

**This covers 99% of use cases!**

### When Authentication IS Required
Authentication is only needed for:
- 🔒 Age-restricted videos
- 🔒 Private videos
- 🔒 Members-only content
- 🔒 Region-locked content
- 🔒 Personal playlists (Watch Later, Liked videos)

## Docker Images

We provide two Docker images:

### 1. Standard Image (Recommended)
```bash
# Dockerfile - Lightweight Alpine Linux
docker build -t youtubedownloader-api .
```
- ✅ Small size (~200MB)
- ✅ Works for all public videos
- ✅ Manual cookie authentication support
- ❌ No browser automation

### 2. Full Image (With Browser Auth)
```bash
# Dockerfile.full - Includes Playwright
docker build -f Dockerfile.full -t youtubedownloader-api:full .
```
- ✅ Automated browser authentication
- ✅ Can login to Google automatically
- ❌ Larger size (~1.5GB)
- ❌ More complex setup

## Authentication Methods

### Method 1: No Authentication (Default)
Just use the API - it works for public videos!

```bash
curl -X POST http://localhost:8080/api/download/start \
  -H "Content-Type: application/json" \
  -d '{"url": "https://youtube.com/watch?v=VIDEO_ID"}'
```

### Method 2: Manual Cookie Extraction
If you need to download restricted content:

1. Open Chrome/Firefox
2. Login to YouTube
3. Extract cookies (see `extract-cookies.md`)
4. Set cookies in API:

```bash
curl -X POST http://localhost:8080/api/auth/set-cookies \
  -H "Content-Type: application/json" \
  -d '{"cookies": "YOUR_COOKIE_STRING"}'
```

### Method 3: Browser Authentication (Full Image Only)
Only available with `Dockerfile.full`:

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "yourpassword"}'
```

**Note:** This may fail if Google requires captcha or 2FA.

## Environment Variables

For the standard image:
```yaml
ENABLE_BROWSER_AUTH: false  # Browser auth not available
```

For the full image:
```yaml
ENABLE_BROWSER_AUTH: true   # Enable browser auth
BROWSER_HEADLESS: true       # Run browser in headless mode
BROWSER_SESSION_PATH: /app/browser-session  # Session storage
```

## Quick Start

### For Most Users (Public Videos Only)
```bash
# Use the standard lightweight image
docker-compose up -d

# Download a public video
curl -X POST http://localhost:8080/api/download/start \
  -H "Content-Type: application/json" \
  -d '{"url": "https://youtube.com/watch?v=dQw4w9WgXcQ"}'
```

### For Advanced Users (Restricted Content)
```bash
# Use the full image
sed -i 's/Dockerfile/Dockerfile.full/' docker-compose.yml
docker-compose up -d

# Either use browser auth or manual cookies
```

## Comparison with Desktop App

| Feature | Desktop App | API (Standard) | API (Full) |
|---------|------------|----------------|------------|
| Public videos | ✅ | ✅ | ✅ |
| WebView for auth | ✅ Built-in | ❌ | ✅ Playwright |
| Cookie persistence | ✅ Settings.dat | ✅ ENV/Config | ✅ ENV/Config |
| Auto-login | ✅ | ❌ | ✅ |
| Size | ~100MB | ~200MB | ~1.5GB |

## Troubleshooting

### "Video is not available" Error
- The video requires authentication
- Solution: Extract and set cookies, or use browser auth

### Browser Auth Fails
- Google may require captcha or 2FA
- Solution: Use manual cookie extraction instead

### "Browser authentication is not available"
- You're using the standard image
- Solution: Use `Dockerfile.full` or extract cookies manually