version: '3.8'

services:
  youtube-downloader-api:
    build:
      context: .
      dockerfile: Dockerfile.working
    container_name: youtube-downloader-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Optional: Provide YouTube credentials for full access
      # - YOUTUBE_USERNAME=<EMAIL>
      # - YOUTUBE_PASSWORD=yourpassword
      
      # API Configuration
      - ASPNETCORE_ENVIRONMENT=Production
      - EnableSwagger=true
      
      # Download settings
      - DEFAULT_QUALITY=Highest
      - DEFAULT_FORMAT=mp4
      - MAX_PARALLEL_DOWNLOADS=3
      
      # Features
      - ENABLE_SUBTITLES=true
      - ENABLE_TAGS=true
      
      # Browser settings
      - BROWSER_HEADLESS=true
      - ENABLE_BROWSER_AUTH=true
      
    volumes:
      # Persistent storage for downloads
      - ./downloads:/app/downloads
      
      # Persistent config (cookies, settings)
      - ./config:/app/config
      
      # Logs
      - ./logs:/app/logs
      
      # Browser data (session storage)
      - browser-data:/app/browser-data
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  browser-data:
    driver: local