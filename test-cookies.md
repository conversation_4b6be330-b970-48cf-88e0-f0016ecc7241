# Testing YouTube Access with Cookies

## The Issue
All YouTube videos are showing as "not available" when accessed through the API, even public videos like:
- <PERSON> - Never Gonna Give You Up (dQw4w9WgXcQ)
- Me at the zoo - First YouTube video (jNQXAC9IVRw)

## Possible Causes
1. **YouTube now requires cookies/authentication** - Even for public videos
2. **Geo-blocking or rate limiting** - Container IP might be blocked
3. **YoutubeExplode incompatibility** - YouTube changed their page structure
4. **Docker networking issue** - Some specific header or cookie needed

## How to Extract Cookies from Browser

### Method 1: Browser Developer Tools
1. Open YouTube in Chrome/Firefox
2. Login to your account
3. Open Developer Tools (F12)
4. Go to Application/Storage tab
5. Find Cookies section
6. Look for youtube.com cookies
7. Copy the important ones:
   - `VISITOR_INFO1_LIVE`
   - `PREF`
   - `LOGIN_INFO`
   - `SID`, `HSID`, `SSID`, `APISID`, `SAPISID`

### Method 2: Browser Extension
Use extensions like "Get cookies.txt" or "EditThisCookie"

### Method 3: Using the provided script
See `extract-cookies.md` for details

## Setting Cookies in the API

```bash
# Format cookies as JSON array
curl -X POST http://localhost:8081/api/auth/set-cookies \
  -H "Content-Type: application/json" \
  -d '{
    "cookies": "[{\"Name\":\"VISITOR_INFO1_LIVE\",\"Value\":\"YOUR_VALUE\",\"Domain\":\".youtube.com\",\"Path\":\"/\"}]"
  }'
```

## Testing if it's a Cookie Issue

To determine if cookies are the problem:
1. Try accessing YouTube in an incognito browser - if videos work there, cookies aren't required
2. Try the desktop app without login - if it works, the issue is specific to our Docker setup
3. Extract cookies and set them in the API - if it works, YouTube now requires auth