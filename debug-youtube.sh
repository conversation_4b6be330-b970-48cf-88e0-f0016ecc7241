#!/bin/bash

echo "Debugging YouTube access issue..."
echo "================================"

# Test 1: Check if it's a network issue
echo -e "\n1. Testing network connectivity to YouTube..."
docker exec $(docker ps -q -f name=youtube-downloader-api) curl -I -s https://www.youtube.com | head -1

# Test 2: Check container logs for more details
echo -e "\n2. Recent container logs:"
docker logs $(docker ps -q -f name=youtube-downloader-api) 2>&1 | tail -20

# Test 3: Check if the issue is cookie-related
echo -e "\n3. Testing with cookies..."
curl -X POST http://localhost:8080/api/auth/status

echo -e "\n4. Testing query resolution directly..."
curl -X POST http://localhost:8080/api/query/resolve \
  -H "Content-Type: application/json" \
  -d '{"query": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}'

echo -e "\n================================"
echo "Debug complete!"