#!/bin/bash

# Test if cookies fix the YouTube access issue
echo "Testing if cookies resolve YouTube access..."

# These are example cookie values - you need to replace with real ones
# You can get these from your browser after logging into YouTube

# Try setting minimal cookies that might be needed
curl -X POST http://localhost:8081/api/auth/set-cookies \
  -H "Content-Type: application/json" \
  -d '{
    "cookies": "VISITOR_INFO1_LIVE=YOUR_VALUE; CONSENT=YES+"
  }'

echo -e "\nChecking auth status..."
curl -s http://localhost:8081/api/auth/status | python3 -m json.tool

echo -e "\nTrying download with cookies..."
curl -X POST http://localhost:8081/api/download/start \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "container": "mp4"
  }'

echo -e "\n\nTo get real cookies:"
echo "1. Open YouTube in Chrome"
echo "2. Open DevTools (F12)"
echo "3. Go to Application > Cookies"
echo "4. Copy VISITOR_INFO1_LIVE and other cookies"
echo "5. Format as: 'name1=value1; name2=value2'"