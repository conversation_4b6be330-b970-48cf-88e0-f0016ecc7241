<UserControl
    x:Class="YoutubeDownloader.Views.Dialogs.DownloadSingleSetupView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:asyncImageLoader="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
    xmlns:converters="clr-namespace:YoutubeDownloader.Converters"
    xmlns:dialogs="clr-namespace:YoutubeDownloader.ViewModels.Dialogs"
    xmlns:materialAssists="clr-namespace:Material.Styles.Assists;assembly=Material.Styles"
    xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
    x:Name="UserControl"
    Width="500"
    x:DataType="dialogs:DownloadSingleSetupViewModel"
    Loaded="UserControl_OnLoaded">
    <Grid RowDefinitions="Auto,*,Auto,Auto">
        <!--  Info  -->
        <StackPanel
            Grid.Row="0"
            Margin="16"
            Orientation="Vertical">
            <!--  Title  -->
            <TextBlock
                FontSize="19"
                Text="{Binding Video.Title}"
                TextTrimming="CharacterEllipsis"
                ToolTip.Tip="{Binding Video.Title}">
                <TextBlock.ContextMenu>
                    <ContextMenu>
                        <MenuItem Command="{Binding CopyTitleCommand}" Header="Copy" />
                    </ContextMenu>
                </TextBlock.ContextMenu>
            </TextBlock>

            <StackPanel Margin="0,8,0,0" Orientation="Horizontal">
                <!--  Author  -->
                <StackPanel Orientation="Horizontal">
                    <materialIcons:MaterialIcon
                        Width="16"
                        Height="16"
                        BorderThickness="1"
                        Kind="UserOutline" />
                    <TextBlock
                        Margin="3,0,0,0"
                        FontWeight="Light"
                        Text="{Binding Video.Author.Title}"
                        TextTrimming="CharacterEllipsis" />
                </StackPanel>

                <!--  Duration  -->
                <StackPanel Margin="16,0,0,0" Orientation="Horizontal">
                    <materialIcons:MaterialIcon
                        Width="16"
                        Height="16"
                        BorderThickness="1"
                        Kind="ClockOutline" />
                    <TextBlock
                        Margin="3,0,0,0"
                        FontWeight="Light"
                        Text="{Binding Video.Duration, TargetNullValue=Live}"
                        TextTrimming="CharacterEllipsis" />
                </StackPanel>
            </StackPanel>
        </StackPanel>

        <!--  Thumbnail  -->
        <Border
            Grid.Row="1"
            BorderBrush="{DynamicResource MaterialDividerBrush}"
            BorderThickness="0,1">
            <Image asyncImageLoader:ImageLoader.Source="{Binding Video, Converter={x:Static converters:VideoToHighestQualityThumbnailUrlStringConverter.Instance}}" />
        </Border>

        <!--  Download options  -->
        <ComboBox
            Grid.Row="2"
            Margin="16"
            materialAssists:ComboBoxAssist.Label="Format"
            DockPanel.Dock="Left"
            ItemsSource="{Binding AvailableDownloadOptions}"
            SelectedItem="{Binding SelectedDownloadOption}"
            Theme="{DynamicResource MaterialFilledComboBox}">
            <ComboBox.ItemTemplate>
                <DataTemplate>
                    <TextBlock>
                        <!--  Video quality  -->
                        <Run Classes.audioOnly="{Binding IsAudioOnly}">
                            <Run.Styles>
                                <Style Selector="Run">
                                    <Setter Property="Text" Value="{Binding VideoQuality, Mode=OneWay}" />

                                    <Style Selector="^.audioOnly">
                                        <Setter Property="Text" Value="Audio" />
                                    </Style>
                                </Style>
                            </Run.Styles>
                        </Run>

                        <!--  Separator  -->
                        <Run Text="—" />

                        <!--  Container  -->
                        <Run Text="{Binding Container, Mode=OneWay}" />
                    </TextBlock>
                </DataTemplate>
            </ComboBox.ItemTemplate>
        </ComboBox>

        <!--  Buttons  -->
        <StackPanel
            Grid.Row="3"
            Margin="16,8,16,16"
            HorizontalAlignment="Right"
            Orientation="Horizontal">
            <!--  Download  -->
            <Button
                Margin="0,0,8,0"
                Command="{Binding ConfirmCommand}"
                Content="DOWNLOAD"
                IsDefault="True"
                Theme="{DynamicResource MaterialOutlineButton}" />

            <!--  Cancel  -->
            <Button
                Margin="8,0,0,0"
                Command="{Binding CloseCommand}"
                Content="CANCEL"
                IsCancel="True"
                Theme="{DynamicResource MaterialOutlineButton}" />
        </StackPanel>
    </Grid>
</UserControl>
