<Window
    x:Class="YoutubeDownloader.Views.MainView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:dialogHostAvalonia="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
    xmlns:materialStyles="clr-namespace:Material.Styles.Controls;assembly=Material.Styles"
    xmlns:viewModels="clr-namespace:YoutubeDownloader.ViewModels"
    Title="{Binding Title}"
    Width="720"
    Height="620"
    MinWidth="600"
    MinHeight="400"
    x:DataType="viewModels:MainViewModel"
    Icon="/favicon.ico"
    RenderOptions.BitmapInterpolationMode="HighQuality"
    WindowStartupLocation="CenterScreen">
    <!--  Hack: dialog host animations mess up webview positioning, so need to disable them  -->
    <dialogHostAvalonia:DialogHost
        x:Name="DialogHost"
        CloseOnClickAway="False"
        DisableOpeningAnimation="True"
        Loaded="DialogHost_OnLoaded">
        <materialStyles:SnackbarHost HostName="Root" SnackbarMaxCounts="3">
            <ContentControl Content="{Binding Dashboard}" />
        </materialStyles:SnackbarHost>
    </dialogHostAvalonia:DialogHost>
</Window>
