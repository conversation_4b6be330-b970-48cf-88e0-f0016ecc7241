<Application
    x:Class="YoutubeDownloader.App"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:dialogHostAvalonia="clr-namespace:DialogHostAvalonia;assembly=DialogHost.Avalonia"
    xmlns:framework="clr-namespace:YoutubeDownloader.Framework"
    xmlns:materialAssists="clr-namespace:Material.Styles.Assists;assembly=Material.Styles"
    xmlns:materialControls="clr-namespace:Material.Styles.Controls;assembly=Material.Styles"
    xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
    xmlns:materialStyles="clr-namespace:Material.Styles.Themes;assembly=Material.Styles"
    Name="YoutubeDownloader"
    ActualThemeVariantChanged="Application_OnActualThemeVariantChanged">
    <Application.DataTemplates>
        <framework:ViewManager />
    </Application.DataTemplates>

    <Application.Styles>
        <!--  This theme is used as a stub to pre-load default resources, the actual colors are set through code  -->
        <materialStyles:MaterialTheme
            BaseTheme="Light"
            PrimaryColor="Grey"
            SecondaryColor="DeepOrange" />
        <materialIcons:MaterialIconStyles />
        <dialogHostAvalonia:DialogHostStyles />

        <!--  Combo box  -->
        <Style Selector="ComboBox">
            <Setter Property="FontSize" Value="14" />

            <Style Selector="^ /template/ Panel#PART_RootPanel">
                <Setter Property="Height" Value="22" />
            </Style>

            <Style Selector="^ /template/ ToggleButton">
                <Style Selector="^:checked, ^:unchecked">
                    <Setter Property="Margin" Value="0" />
                    <Setter Property="CornerRadius" Value="0" />

                    <Style Selector="^ ContentPresenter#contentPresenter">
                        <Setter Property="Margin" Value="12,8" />
                    </Style>
                </Style>
            </Style>
        </Style>

        <!--  Context menu  -->
        <Style Selector="ContextMenu">
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDividerBrush}" />
            <Setter Property="BorderThickness" Value="1" />
        </Style>

        <!--  Data grid  -->
        <Style Selector="DataGrid">
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDividerBrush}" />
            <Setter Property="AutoGenerateColumns" Value="False" />
            <Setter Property="CanUserReorderColumns" Value="False" />
            <Setter Property="CanUserResizeColumns" Value="False" />
            <Setter Property="CanUserSortColumns" Value="True" />
            <Setter Property="IsReadOnly" Value="True" />
            <Setter Property="SelectionMode" Value="Single" />
        </Style>

        <Style Selector="DataGridColumnHeader">
            <Setter Property="AreSeparatorsVisible" Value="False" />
        </Style>

        <Style Selector="DataGridRow">
            <Style Selector="^:selected /template/ Rectangle#BackgroundRectangle">
                <Setter Property="IsVisible" Value="False" />
            </Style>
            <Style Selector="^:pointerover /template/ Rectangle#BackgroundRectangle">
                <Setter Property="IsVisible" Value="False" />
            </Style>
        </Style>

        <!--  Dialog host  -->
        <Style Selector="dialogHostAvalonia|DialogHost">
            <Setter Property="DialogMargin" Value="0" />
        </Style>

        <Style Selector="dialogHostAvalonia|DialogOverlayPopupHost">
            <Setter Property="Margin" Value="48" />
            <Setter Property="Background" Value="{DynamicResource MaterialPaperBrush}" />
        </Style>

        <!--  Snack bar host  -->
        <Style Selector="materialControls|SnackbarHost">
            <Setter Property="SnackbarHorizontalAlignment" Value="Stretch" />
            <Setter Property="VerticalContentAlignment" Value="Center" />

            <Style Selector="^ /template/ ItemsControl#PART_SnackbarHostItemsContainer materialControls|Card">
                <Setter Property="Background" Value="{DynamicResource MaterialDarkBackgroundBrush}" />
                <Setter Property="Foreground" Value="{DynamicResource MaterialDarkForegroundBrush}" />
            </Style>

            <Style Selector="^ /template/ ItemsControl#PART_SnackbarHostItemsContainer Button">
                <Setter Property="Foreground" Value="{DynamicResource SecondaryHueMidBrush}" />
            </Style>
        </Style>

        <!--  Progress bar  -->
        <Style Selector="ProgressBar">
            <Setter Property="Minimum" Value="0" />
            <Setter Property="Maximum" Value="1" />
            <Setter Property="Foreground" Value="{DynamicResource MaterialSecondaryMidBrush}" />
            <Setter Property="materialAssists:TransitionAssist.DisableTransitions" Value="True" />

            <Style Selector="^:horizontal">
                <Setter Property="MinHeight" Value="0" />
            </Style>
        </Style>

        <!--  Slider  -->
        <Style Selector="Slider">
            <Style Selector="^ /template/ ProgressBar#PART_ProgressLayer">
                <Style Selector="^:horizontal">
                    <Style Selector="^ Panel#PART_InnerPanel">
                        <Setter Property="Height" Value="2" />

                        <Style Selector="^ Border#PART_InactiveState">
                            <Setter Property="Margin" Value="0" />
                            <Setter Property="Height" Value="2" />
                        </Style>

                        <Style Selector="^ Border#PART_Indicator">
                            <Setter Property="Margin" Value="0" />
                        </Style>
                    </Style>
                </Style>
            </Style>

            <Style Selector="^ /template/ Track#PART_Track">
                <Style Selector="^:horizontal">
                    <Setter Property="Margin" Value="4,0" />
                </Style>

                <Style Selector="^ Border#PART_HoverEffect">
                    <Setter Property="Width" Value="24" />
                    <Setter Property="Height" Value="24" />
                </Style>

                <Style Selector="^ Border#PART_ThumbGrip">
                    <Setter Property="Width" Value="12" />
                    <Setter Property="Height" Value="12" />
                </Style>
            </Style>
        </Style>

        <!--  Text box  -->
        <Style Selector="TextBox">
            <Setter Property="FontSize" Value="14" />
        </Style>

        <!--  Toggle switch  -->
        <Style Selector="ToggleSwitch">
            <Setter Property="materialAssists:ToggleSwitchAssist.SwitchThumbOffBackground" Value="{DynamicResource ToggleBackgroundBrush}" />
        </Style>

        <!--  Tooltip  -->
        <Style Selector="ToolTip">
            <Setter Property="TextElement.FontSize" Value="14" />
            <Setter Property="TextElement.FontWeight" Value="Normal" />
            <Setter Property="TextElement.FontStyle" Value="Normal" />
            <Setter Property="TextElement.FontStretch" Value="Normal" />
        </Style>
    </Application.Styles>

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Default">
                    <SolidColorBrush x:Key="ToggleBackgroundBrush" Color="#FFFFFF" />
                </ResourceDictionary>
                <ResourceDictionary x:Key="Dark">
                    <SolidColorBrush x:Key="ToggleBackgroundBrush" Color="#8E8E8E" />
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>

            <!--  Text box  -->
            <ControlTheme
                x:Key="CompactTextBox"
                BasedOn="{StaticResource {x:Type TextBox}}"
                TargetType="{x:Type TextBox}">
                <Styles>
                    <Style Selector="TextBox">
                        <Setter Property="Height" Value="22" />

                        <Style Selector="^ /template/ Panel#PART_TextFieldPanel">
                            <Setter Property="MinHeight" Value="0" />
                        </Style>

                        <Style Selector="^ /template/ Panel#PART_TextContainer">
                            <Setter Property="Margin" Value="0" />
                        </Style>
                    </Style>
                </Styles>
            </ControlTheme>
        </ResourceDictionary>
    </Application.Resources>
</Application>