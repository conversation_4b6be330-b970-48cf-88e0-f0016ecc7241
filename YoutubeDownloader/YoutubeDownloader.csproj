<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <ApplicationIcon>..\favicon.ico</ApplicationIcon>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <!-- Trimmed builds break support for Windows 10 for some reason -->
    <!-- https://github.com/Tyrrrz/YoutubeDownloader/issues/496 -->
    <PublishTrimmed>false</PublishTrimmed>
    <CopyOutputSymbolsToPublishDirectory>false</CopyOutputSymbolsToPublishDirectory>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
  </PropertyGroup>
  <PropertyGroup>
    <DownloadFFmpeg>true</DownloadFFmpeg>
  </PropertyGroup>
  <ItemGroup>
    <AvaloniaResource Include="..\favicon.ico" Link="favicon.ico" />
  </ItemGroup>
  <ItemGroup>
    <None
      Include="ffmpeg.exe"
      CopyToOutputDirectory="PreserveNewest"
      Condition="Exists('ffmpeg.exe')"
    />
    <None Include="ffmpeg" CopyToOutputDirectory="PreserveNewest" Condition="Exists('ffmpeg')" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AsyncImageLoader.Avalonia" Version="3.3.0" />
    <PackageReference Include="Avalonia" Version="11.3.0" />
    <PackageReference Include="Avalonia.Controls.DataGrid" Version="11.3.0" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.0" />
    <PackageReference
      Include="Avalonia.Diagnostics"
      Version="11.3.0"
      Condition="'$(Configuration)' == 'Debug'"
    />
    <PackageReference Include="Cogwheel" Version="2.1.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="CSharpier.MsBuild" Version="1.0.2" PrivateAssets="all" />
    <PackageReference Include="Deorcify" Version="1.1.0" PrivateAssets="all" />
    <PackageReference Include="DialogHost.Avalonia" Version="0.9.3" />
    <PackageReference Include="Gress" Version="2.1.1" />
    <PackageReference Include="Material.Avalonia" Version="3.9.2" />
    <PackageReference Include="Material.Avalonia.DataGrid" Version="3.9.2" />
    <PackageReference Include="Material.Icons.Avalonia" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.3" />
    <PackageReference Include="Onova" Version="2.6.13" />
    <PackageReference Include="WebView.Avalonia" Version="********" />
    <PackageReference Include="WebView.Avalonia.Desktop" Version="********" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\YoutubeDownloader.Core\YoutubeDownloader.Core.csproj" />
  </ItemGroup>
  <!-- Avalonia.WebView is completely incompatible with trimming -->
  <ItemGroup>
    <TrimmerRootAssembly Include="Avalonia.WebView" />
    <TrimmerRootAssembly Include="Avalonia.WebView.Desktop" />
    <TrimmerRootAssembly Include="Avalonia.WebView.Linux" />
    <TrimmerRootAssembly Include="Avalonia.WebView.MacCatalyst" />
    <TrimmerRootAssembly Include="Avalonia.WebView.Windows" />
    <TrimmerRootAssembly Include="AvaloniaWebView.Shared" />
    <TrimmerRootAssembly Include="Linux.WebView.Core" />
    <TrimmerRootAssembly Include="Microsoft.Web.WebView2.Core" />
    <TrimmerRootAssembly Include="WebView.Avalonia" />
    <TrimmerRootAssembly Include="WebView.Core" />
  </ItemGroup>
  <Target
    Name="DownloadFFmpeg"
    BeforeTargets="Restore;PreBuildEvent"
    Condition="$(DownloadFFmpeg) AND !Exists('ffmpeg.exe') AND !Exists('ffmpeg')"
  >
    <Exec
      Command="pwsh -ExecutionPolicy Bypass -File $(ProjectDir)/Download-FFmpeg.ps1 -Platform $(RuntimeIdentifier) -OutputPath $(ProjectDir)"
      LogStandardErrorAsError="true"
      Condition="'$(RuntimeIdentifier)' != ''"
    />
    <Exec
      Command="pwsh -ExecutionPolicy Bypass -File $(ProjectDir)/Download-FFmpeg.ps1 -OutputPath $(ProjectDir)"
      LogStandardErrorAsError="true"
      Condition="'$(RuntimeIdentifier)' == ''"
    />
  </Target>
  <Target Name="PublishMacOSBundle" AfterTargets="Publish" Condition="$(PublishMacOSBundle)">
    <Exec
      Command="pwsh -ExecutionPolicy Bypass -File $(ProjectDir)/Publish-MacOSBundle.ps1 -PublishDirPath $(PublishDir) -IconsFilePath $(ProjectDir)/../favicon.icns -FullVersion $(Version) -ShortVersion $(AssemblyVersion)"
      LogStandardErrorAsError="true"
    />
  </Target>
</Project>
