#!/bin/bash

# Test YoutubeDownloader API

API_URL="http://localhost:8081"

echo "Testing YoutubeDownloader API..."
echo "================================"

# Test 1: Health check
echo -e "\n1. Testing health endpoint..."
curl -s "$API_URL/api/health/live" && echo " ✓ API is live" || echo " ✗ API is not responding"

# Test 2: Try downloading a public video (<PERSON> Roll - always available)
echo -e "\n2. Testing download with public video (<PERSON> - Never Gonna Give You Up)..."
response=$(curl -s -X POST "$API_URL/api/download/start" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "container": "mp4",
    "waitForCompletion": false
  }')

if echo "$response" | grep -q "downloadId"; then
  echo " ✓ Download started successfully"
  echo " Response: $response"
else
  echo " ✗ Download failed"
  echo " Response: $response"
fi

# Test 3: Try with just video ID
echo -e "\n3. Testing with video ID only..."
response=$(curl -s -X POST "$API_URL/api/download/start" \
  -H "Content-Type: application/json" \
  -d '{
    "videoId": "dQw4w9WgXcQ",
    "container": "mp4"
  }')

if echo "$response" | grep -q "downloadId"; then
  echo " ✓ Download started successfully"
  echo " Response: $response"
else
  echo " ✗ Download failed"
  echo " Response: $response"
fi

# Test 4: Test with unavailable video (should fail gracefully)
echo -e "\n4. Testing with unavailable video (expected to fail)..."
response=$(curl -s -X POST "$API_URL/api/download/start" \
  -H "Content-Type: application/json" \
  -d '{
    "videoId": "n1Ttq-YzFU4",
    "container": "mp4"
  }')

if echo "$response" | grep -q "not available\|error"; then
  echo " ✓ Correctly reported video unavailable"
  echo " Response: $response"
else
  echo " ✗ Unexpected response"
  echo " Response: $response"
fi

echo -e "\n================================"
echo "Test complete!"