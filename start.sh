#!/bin/bash

# YoutubeDownloader API Docker Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
MODE="development"
BUILD=false
DETACH=""

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --prod|--production)
            MODE="production"
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        -d|--detach)
            DETACH="-d"
            shift
            ;;
        --help)
            echo "Usage: ./start.sh [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --prod, --production  Run in production mode"
            echo "  --build              Force rebuild of Docker images"
            echo "  -d, --detach         Run in background"
            echo "  --help               Show this help message"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check Docker installation
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}Error: Docker Compose is not installed${NC}"
    exit 1
fi

# Determine docker-compose command
if docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

echo -e "${GREEN}Starting YoutubeDownloader API in ${MODE} mode...${NC}"

# Create necessary directories
echo "Creating directories..."
if [ "$MODE" = "development" ]; then
    mkdir -p dev-downloads dev-logs dev-config dev-temp
else
    sudo mkdir -p /var/youtube-downloader/{downloads,logs,config}
    sudo chown -R 1000:1000 /var/youtube-downloader
fi

# Build if requested
if [ "$BUILD" = true ]; then
    echo -e "${YELLOW}Building Docker images...${NC}"
    if [ "$MODE" = "production" ]; then
        $COMPOSE_CMD -f docker-compose.yml -f docker-compose.prod.yml build
    else
        $COMPOSE_CMD build
    fi
fi

# Start services
echo -e "${GREEN}Starting services...${NC}"
if [ "$MODE" = "production" ]; then
    $COMPOSE_CMD -f docker-compose.yml -f docker-compose.prod.yml up $DETACH
else
    $COMPOSE_CMD up $DETACH
fi

# Show status if running in detached mode
if [ -n "$DETACH" ]; then
    echo ""
    echo -e "${GREEN}Services started successfully!${NC}"
    echo ""
    echo "API endpoints:"
    if [ "$MODE" = "production" ]; then
        echo "  - API: http://localhost:8080"
        echo "  - Health: http://localhost:8080/api/health"
    else
        echo "  - API: http://localhost:5000"
        echo "  - Swagger: http://localhost:5000/swagger"
        echo "  - Health: http://localhost:5000/api/health"
    fi
    echo ""
    echo "Commands:"
    echo "  - View logs: $COMPOSE_CMD logs -f"
    echo "  - Stop services: $COMPOSE_CMD down"
    echo "  - View status: $COMPOSE_CMD ps"
fi