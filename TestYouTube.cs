using System;
using System.Net.Http;
using System.Threading.Tasks;
using YoutubeExplode;
using YoutubeExplode.Videos;

class TestYouTube
{
    static async Task Main()
    {
        try
        {
            Console.WriteLine("Testing YouTube access...");
            
            // Test 1: With default HttpClient
            Console.WriteLine("\n1. Testing with default HttpClient:");
            var youtube1 = new YoutubeClient();
            try
            {
                var video1 = await youtube1.Videos.GetAsync("dQw4w9WgXcQ");
                Console.WriteLine($"✓ Success: {video1.Title}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed: {ex.Message}");
            }
            
            // Test 2: With custom HttpClient (like our app)
            Console.WriteLine("\n2. Testing with custom HttpClient:");
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("User-Agent", "YoutubeDownloader/1.0.0");
            var youtube2 = new YoutubeClient(httpClient);
            try
            {
                var video2 = await youtube2.Videos.GetAsync("dQw4w9WgXcQ");
                Console.WriteLine($"✓ Success: {video2.Title}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed: {ex.Message}");
            }
            
            // Test 3: With browser-like user agent
            Console.WriteLine("\n3. Testing with browser user agent:");
            var httpClient3 = new HttpClient();
            httpClient3.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            var youtube3 = new YoutubeClient(httpClient3);
            try
            {
                var video3 = await youtube3.Videos.GetAsync("dQw4w9WgXcQ");
                Console.WriteLine($"✓ Success: {video3.Title}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed: {ex.Message}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex}");
        }
    }
}