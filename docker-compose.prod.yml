# Production configuration - use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
version: '3.8'

services:
  youtube-downloader-api:
    image: youtube-downloader:prod
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
      cache_from:
        - youtube-downloader:prod
        - youtube-downloader:latest
    restart: always
    environment:
      # Production optimizations
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - DOTNET_EnableDiagnostics=0
      - DOTNET_CLI_TELEMETRY_OPTOUT=1
      
      # Production paths
      - DOWNLOAD_PATH=/data/downloads
      - TEMP_PATH=/data/temp
      
      # Production settings
      - MAX_PARALLEL_DOWNLOADS=5
      - DEFAULT_QUALITY=Highest
      - DEFAULT_FORMAT=mp4
      
      # Security
      - EnableSwagger=false
      
      # Production logging
      - Logging__LogLevel__Default=Warning
      - Logging__LogLevel__Microsoft.AspNetCore=Error
      - Serilog__MinimumLevel__Default=Warning
      
      # Cleanup
      - CLEANUP_ENABLED=true
      - CLEANUP_INTERVAL_HOURS=6
      - MAX_FILE_RETENTION_DAYS=3
      
    volumes:
      # Named volumes for production
      - youtube-downloads:/data/downloads
      - youtube-logs:/data/logs
      - youtube-config:/data/config
      - youtube-temp:/data/temp
      
    deploy:
      mode: replicated
      replicas: 1
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          cpus: '4'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 1G
          
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
        compress: "true"
        
    networks:
      - youtube-network
      
  # Nginx reverse proxy with SSL termination
  nginx:
    image: nginx:alpine
    container_name: youtube-downloader-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - youtube-downloads:/usr/share/nginx/html/downloads:ro
    depends_on:
      - youtube-downloader-api
    networks:
      - youtube-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
          
volumes:
  youtube-downloads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/youtube-downloader/downloads
      
  youtube-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/youtube-downloader/logs
      
  youtube-config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/youtube-downloader/config
      
  youtube-temp:
    driver: local
    driver_opts:
      type: tmpfs
      device: tmpfs
      o: size=2g,uid=1000,gid=1000
      
networks:
  youtube-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16