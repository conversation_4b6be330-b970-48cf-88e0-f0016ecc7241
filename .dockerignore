# Git
.git
.gitignore
.gitattributes

# IDE
.vs/
.vscode/
.idea/
*.user
*.suo

# Build output
**/bin/
**/obj/
**/out/
**/publish/

# Test results
TestResults/
*.trx
*.coverage

# Documentation
*.md
LICENSE
CONTRIBUTING

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
azure-pipelines.yml

# OS files
.DS_Store
Thumbs.db
*.swp

# Logs
*.log
logs/

# Temporary files
*.tmp
temp/

# Downloads (don't include existing downloads in image)
downloads/

# Desktop application (we only need API)
YoutubeDownloader/
!YoutubeDownloader.Core/

# Development files
*.Development.json
launchSettings.json

# FFmpeg binaries (will be installed via package manager)
ffmpeg.exe
ffmpeg