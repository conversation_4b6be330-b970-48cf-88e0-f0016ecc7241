name: main

on:
  workflow_dispatch:
  push:
    branches:
      - master
    tags:
      - "*"
  pull_request:
    branches:
      - master

env:
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: true
  DOTNET_NOLOGO: true
  DOTNET_CLI_TELEMETRY_OPTOUT: true

jobs:
  format:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    permissions:
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Install .NET
        uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9 # v4.3.1
        with:
          dotnet-version: 9.0.x

      # Build the project separately to discern between build and format errors
      - name: Build
        run: >
          dotnet build
          -p:CSharpier_Bypass=true
          --configuration Release

      - name: Verify formatting
        id: verify
        run: >
          dotnet build
          -t:CSharpierFormat
          --configuration Release
          --no-restore

      - name: Report issues
        if: ${{ failure() && steps.verify.outcome == 'failure' }}
        run: echo "::error title=Bad formatting::Formatting issues detected. Please build the solution locally to fix them."

  pack:
    strategy:
      matrix:
        rid:
          - win-arm64
          - win-x86
          - win-x64
          - linux-arm64
          # Linux x86 is not supported by .NET
          # - linux-x86
          - linux-x64
          - osx-arm64
          - osx-x64
        bundle-ffmpeg:
          - true
          - false
        include:
          - bundle-ffmpeg: true
            artifact-name-base: YoutubeDownloader
          - bundle-ffmpeg: false
            artifact-name-base: YoutubeDownloader.Bare

    runs-on: ${{ startsWith(matrix.rid, 'win-') && 'windows-latest' || startsWith(matrix.rid, 'osx-') && 'macos-latest' || 'ubuntu-latest' }}
    timeout-minutes: 10

    permissions:
      actions: write
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Install .NET
        uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9 # v4.3.1
        with:
          dotnet-version: 9.0.x

      - name: Publish app
        run: >
          dotnet publish YoutubeDownloader
          -p:Version=${{ github.ref_type == 'tag' && github.ref_name || format('999.9.9-ci-{0}', github.sha) }}
          -p:CSharpier_Bypass=true
          -p:DownloadFFmpeg=${{ matrix.bundle-ffmpeg }}
          -p:PublishMacOSBundle=${{ startsWith(matrix.rid, 'osx-') }}
          --output YoutubeDownloader/bin/publish
          --configuration Release
          --runtime ${{ matrix.rid }}
          --self-contained

      - name: Upload app binaries
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: ${{ matrix.artifact-name-base }}.${{ matrix.rid }}
          path: YoutubeDownloader/bin/publish
          if-no-files-found: error

  release:
    if: ${{ github.ref_type == 'tag' }}

    needs:
      - format
      - pack

    runs-on: ubuntu-latest
    timeout-minutes: 10

    permissions:
      contents: write

    steps:
      - name: Create release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: >
          gh release create ${{ github.ref_name }}
          --repo ${{ github.event.repository.full_name }}
          --title ${{ github.ref_name }}
          --generate-notes
          --verify-tag

  deploy:
    needs: release

    strategy:
      matrix:
        rid:
          - win-arm64
          - win-x86
          - win-x64
          - linux-arm64
          # Linux x86 is not supported by .NET
          # - linux-x86
          - linux-x64
          - osx-arm64
          - osx-x64
        bundle-ffmpeg:
          - true
          - false
        include:
          - bundle-ffmpeg: true
            artifact-name-base: YoutubeDownloader
          - bundle-ffmpeg: false
            artifact-name-base: YoutubeDownloader.Bare

    runs-on: ubuntu-latest
    timeout-minutes: 10

    permissions:
      actions: read
      contents: write

    steps:
      - name: Download app binaries
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: ${{ matrix.artifact-name-base }}.${{ matrix.rid }}
          path: YoutubeDownloader/

      - name: Set permissions
        if: ${{ !startsWith(matrix.rid, 'win-') }}
        run: |
          [ -f YoutubeDownloader/YoutubeDownloader ] && chmod +x YoutubeDownloader/YoutubeDownloader
          [ -f YoutubeDownloader/ffmpeg ] && chmod +x YoutubeDownloader/ffmpeg
          
          # macOS bundle
          [ -f YoutubeDownloader/YoutubeDownloader.app/Contents/MacOS/YoutubeDownloader] && chmod +x YoutubeDownloader/YoutubeDownloader.app/Contents/MacOS/YoutubeDownloader
          [ -f YoutubeDownloader/YoutubeDownloader.app/Contents/MacOS/ffmpeg ] && chmod +x YoutubeDownloader/YoutubeDownloader.app/Contents/MacOS/ffmpeg

      - name: Create package
        # Change into the artifacts directory to avoid including the directory itself in the zip archive
        working-directory: YoutubeDownloader/
        run: zip -r ../${{ matrix.artifact-name-base }}.${{ matrix.rid }}.zip .

      - name: Upload release asset
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: >
          gh release upload ${{ github.ref_name }}
          ${{ matrix.artifact-name-base }}.${{ matrix.rid }}.zip
          --repo ${{ github.event.repository.full_name }}

  notify:
    needs: deploy
    runs-on: ubuntu-latest
    timeout-minutes: 10

    permissions:
      contents: read

    steps:
      - name: Notify Discord
        uses: tyrrrz/action-http-request@1dd7ad841a34b9299f3741f7c7399f9feefdfb08 # 1.1.3
        with:
          url: ${{ secrets.DISCORD_WEBHOOK }}
          method: POST
          headers: |
            Content-Type: application/json; charset=UTF-8
          body: |
            {
              "avatar_url": "https://raw.githubusercontent.com/${{ github.event.repository.full_name }}/${{ github.ref_name }}/favicon.png",
              "content": "[**${{ github.event.repository.name }}**](<${{ github.event.repository.html_url }}>) v${{ github.ref_name }} has been released!"
            }
          retry-count: 5
