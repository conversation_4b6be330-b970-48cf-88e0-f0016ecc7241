using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace YoutubeDownloader.Api.Hubs;

public class DownloadProgressHub : Hub
{
    private readonly ILogger<DownloadProgressHub> _logger;

    public DownloadProgressHub(ILogger<DownloadProgressHub> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogDebug("Client connected: {ConnectionId}", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogDebug("Client disconnected: {ConnectionId}", Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }

    public async Task SubscribeToDownload(string downloadId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"download-{downloadId}");
        _logger.LogDebug("Client {ConnectionId} subscribed to download {DownloadId}", Context.ConnectionId, downloadId);
    }

    public async Task UnsubscribeFromDownload(string downloadId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"download-{downloadId}");
        _logger.LogDebug("Client {ConnectionId} unsubscribed from download {DownloadId}", Context.ConnectionId, downloadId);
    }
}