{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "DOWNLOAD_PATH": "downloads", "MAX_PARALLEL_DOWNLOADS": 2, "DEFAULT_QUALITY": "Highest", "DEFAULT_FORMAT": "mp4", "FFMPEG_PATH": "ffmpeg", "ENABLE_SUBTITLES": true, "ENABLE_TAGS": true, "INCLUDE_LANGUAGE_SPECIFIC_AUDIO": true, "SKIP_EXISTING_FILES": false, "FILE_NAME_TEMPLATE": "$title", "CLEANUP_ENABLED": true, "CLEANUP_INTERVAL_HOURS": 24, "MAX_FILE_RETENTION_DAYS": 7, "EnableSwagger": true, "AUTH_COOKIES": ""}