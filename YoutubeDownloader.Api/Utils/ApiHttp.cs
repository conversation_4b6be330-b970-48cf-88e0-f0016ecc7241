using System.Net.Http;
using System.Net.Http.Headers;

namespace YoutubeDownloader.Api.Utils;

/// <summary>
/// HTTP client for the API with proper initialization
/// </summary>
public static class ApiHttp
{
    private static readonly HttpClient _client = CreateHttpClient();
    
    private static HttpClient CreateHttpClient()
    {
        var client = new HttpClient();
        
        // Use a browser-like user agent to avoid blocking
        client.DefaultRequestHeaders.UserAgent.Clear();
        client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        
        // Add other headers that might be needed
        client.DefaultRequestHeaders.AcceptLanguage.ParseAdd("en-US,en;q=0.9");
        client.DefaultRequestHeaders.Accept.ParseAdd("text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
        
        return client;
    }
    
    public static HttpClient Client => _client;
}