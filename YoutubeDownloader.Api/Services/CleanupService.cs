using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace YoutubeDownloader.Api.Services;

public class CleanupService : BackgroundService
{
    private readonly IStorageService _storageService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<CleanupService> _logger;
    private readonly TimeSpan _cleanupInterval;
    private readonly TimeSpan _maxFileAge;

    public CleanupService(
        IStorageService storageService,
        IConfiguration configuration,
        ILogger<CleanupService> logger)
    {
        _storageService = storageService;
        _configuration = configuration;
        _logger = logger;
        
        _cleanupInterval = TimeSpan.FromHours(configuration.GetValue<int>("CLEANUP_INTERVAL_HOURS", 24));
        _maxFileAge = TimeSpan.FromDays(configuration.GetValue<int>("MAX_FILE_RETENTION_DAYS", 7));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_configuration.GetValue<bool>("CLEANUP_ENABLED", true))
        {
            _logger.LogInformation("Cleanup service is disabled");
            return;
        }

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                _logger.LogInformation("Starting cleanup of old files");
                await _storageService.CleanupOldFilesAsync(_maxFileAge);
                _logger.LogInformation("Cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cleanup");
            }

            await Task.Delay(_cleanupInterval, stoppingToken);
        }
    }
}