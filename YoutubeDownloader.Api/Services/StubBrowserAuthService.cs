using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace YoutubeDownloader.Api.Services;

/// <summary>
/// Stub implementation when <PERSON>wright is not available
/// </summary>
public class StubBrowserAuthService : IBrowserAuthService
{
    private readonly ILogger<StubBrowserAuthService> _logger;

    public StubBrowserAuthService(ILogger<StubBrowserAuthService> logger)
    {
        _logger = logger;
    }

    public Task<IReadOnlyList<System.Net.Cookie>?> AuthenticateAsync(
        string username, 
        string password, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("Browser authentication is not available in this build. Please use manual cookie extraction.");
        throw new NotSupportedException(
            "Browser authentication requires Play<PERSON>. Use the full Docker image or extract cookies manually.");
    }

    public Task<IReadOnlyList<System.Net.Cookie>?> GetCookiesFromSessionAsync(
        CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("Browser session management is not available in this build.");
        return Task.FromResult<IReadOnlyList<System.Net.Cookie>?>(null);
    }
}