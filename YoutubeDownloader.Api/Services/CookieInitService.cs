using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace YoutubeDownloader.Api.Services;

/// <summary>
/// Service to initialize YouTube cookies on startup
/// </summary>
public class CookieInitService : IHostedService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<CookieInitService> _logger;

    public CookieInitService(IConfiguration configuration, ILogger<CookieInitService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Checking for YouTube cookies...");
        
        var existingCookies = _configuration["AUTH_COOKIES"];
        if (!string.IsNullOrEmpty(existingCookies))
        {
            _logger.LogInformation("Cookies already configured");
            return;
        }

        _logger.LogWarning("=" + new string('=', 78));
        _logger.LogWarning("NO YOUTUBE AUTHENTICATION DETECTED");
        _logger.LogWarning("=" + new string('=', 78));
        _logger.LogWarning("");
        _logger.LogWarning("YouTube now requires authentication to download videos.");
        _logger.LogWarning("");
        _logger.LogWarning("Please authenticate using one of these methods:");
        _logger.LogWarning("");
        _logger.LogWarning("1. AUTOMATED LOGIN (Requires Playwright/Full Docker image):");
        _logger.LogWarning("   POST /api/auth/login");
        _logger.LogWarning("   Body: {{\"username\": \"<EMAIL>\", \"password\": \"yourpass\"}}");
        _logger.LogWarning("");
        _logger.LogWarning("2. MANUAL COOKIE EXTRACTION:");
        _logger.LogWarning("   - Open YouTube in your browser and login");
        _logger.LogWarning("   - Extract cookies using browser DevTools");
        _logger.LogWarning("   - POST /api/auth/set-cookies");
        _logger.LogWarning("   - See /extract-cookies.md for details");
        _logger.LogWarning("");
        _logger.LogWarning("The API will work but downloads will fail until authenticated.");
        _logger.LogWarning("=" + new string('=', 78));
    }

    private async Task<List<Cookie>> GetMinimalYouTubeCookiesAsync()
    {
        var cookies = new List<Cookie>();
        
        try
        {
            using var handler = new HttpClientHandler();
            using var client = new HttpClient(handler);
            
            // Set a browser-like user agent
            client.DefaultRequestHeaders.UserAgent.ParseAdd(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            
            // Visit YouTube homepage to get cookies
            var response = await client.GetAsync("https://www.youtube.com");
            
            if (response.Headers.TryGetValues("Set-Cookie", out var setCookies))
            {
                foreach (var setCookie in setCookies)
                {
                    // Parse basic cookie (simplified parsing)
                    var parts = setCookie.Split(';')[0].Split('=', 2);
                    if (parts.Length == 2)
                    {
                        var name = parts[0].Trim();
                        var value = parts[1].Trim();
                        
                        // Keep important cookies
                        if (name.StartsWith("VISITOR") || name.StartsWith("YSC") || name == "CONSENT")
                        {
                            cookies.Add(new Cookie(name, value, "/", ".youtube.com"));
                        }
                    }
                }
            }
            
            // Add consent cookie if not present (required for EU)
            if (!cookies.Any(c => c.Name == "CONSENT"))
            {
                cookies.Add(new Cookie("CONSENT", "YES+", "/", ".youtube.com"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get minimal cookies");
        }
        
        return cookies;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}