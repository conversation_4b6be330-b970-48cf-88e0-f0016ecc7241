using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace YoutubeDownloader.Api.Services;

public class LocalStorageService : IStorageService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<LocalStorageService> _logger;
    private readonly string _downloadPath;

    public LocalStorageService(IConfiguration configuration, ILogger<LocalStorageService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _downloadPath = configuration["DOWNLOAD_PATH"] ?? Path.Combine(Directory.GetCurrentDirectory(), "downloads");
        
        if (!Directory.Exists(_downloadPath))
        {
            Directory.CreateDirectory(_downloadPath);
            _logger.LogInformation("Created download directory: {Path}", _downloadPath);
        }
    }

    public string GetDownloadPath() => _downloadPath;

    public string GenerateFilePath(string fileName)
    {
        var safeFileName = string.Join("_", fileName.Split(Path.GetInvalidFileNameChars()));
        return Path.Combine(_downloadPath, safeFileName);
    }

    public Task<Stream?> GetFileStreamAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return Task.FromResult<Stream?>(null);

            var stream = File.OpenRead(filePath);
            return Task.FromResult<Stream?>(stream);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading file: {FilePath}", filePath);
            return Task.FromResult<Stream?>(null);
        }
    }

    public Task DeleteFileAsync(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                _logger.LogDebug("Deleted file: {FilePath}", filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
        }
        
        return Task.CompletedTask;
    }

    public Task<long> GetFileSizeAsync(string filePath)
    {
        try
        {
            if (File.Exists(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                return Task.FromResult(fileInfo.Length);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file size: {FilePath}", filePath);
        }
        
        return Task.FromResult(0L);
    }

    public Task CleanupOldFilesAsync(TimeSpan maxAge)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow - maxAge;
            var files = Directory.GetFiles(_downloadPath);
            
            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                if (fileInfo.CreationTimeUtc < cutoffDate)
                {
                    File.Delete(file);
                    _logger.LogInformation("Deleted old file: {FileName}", fileInfo.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cleanup");
        }
        
        return Task.CompletedTask;
    }
}