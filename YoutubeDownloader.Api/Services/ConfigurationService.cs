using System;
using System.Collections.Generic;
using System.Net;
using Microsoft.Extensions.Configuration;
using YoutubeDownloader.Core.Downloading;

namespace YoutubeDownloader.Api.Services;

public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;

    public ConfigurationService(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public int MaxParallelDownloads => 
        _configuration.GetValue<int>("MAX_PARALLEL_DOWNLOADS", 2);

    public VideoQualityPreference DefaultQualityPreference => 
        Enum.TryParse<VideoQualityPreference>(_configuration["DEFAULT_QUALITY"], out var quality) 
            ? quality 
            : VideoQualityPreference.Highest;

    public string DefaultContainer => 
        _configuration["DEFAULT_FORMAT"] ?? "mp4";

    public bool IncludeSubtitles => 
        _configuration.GetValue<bool>("ENABLE_SUBTITLES", true);

    public bool InjectTags => 
        _configuration.GetValue<bool>("ENABLE_TAGS", true);

    public bool IncludeLanguageSpecificAudioStreams => 
        _configuration.GetValue<bool>("INCLUDE_LANGUAGE_SPECIFIC_AUDIO", true);

    public string FileNameTemplate => 
        _configuration["FILE_NAME_TEMPLATE"] ?? "$title";

    public bool SkipExistingFiles => 
        _configuration.GetValue<bool>("SKIP_EXISTING_FILES", false);

    public string FFmpegPath => 
        _configuration["FFMPEG_PATH"] ?? "ffmpeg";

    public IReadOnlyList<Cookie>? GetAuthCookies()
    {
        var cookieString = _configuration["AUTH_COOKIES"];
        if (string.IsNullOrEmpty(cookieString))
            return null;

        try
        {
            // Parse cookies from JSON string format
            // Expected format: [{"Name":"cookie_name","Value":"cookie_value","Domain":".youtube.com","Path":"/"}]
            var cookies = System.Text.Json.JsonSerializer.Deserialize<List<Cookie>>(cookieString);
            return cookies;
        }
        catch
        {
            // If JSON parsing fails, try to parse from a simple format
            // Format: name1=value1; name2=value2
            var cookies = new List<Cookie>();
            var pairs = cookieString.Split(';', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
            
            foreach (var pair in pairs)
            {
                var parts = pair.Split('=', 2);
                if (parts.Length == 2)
                {
                    cookies.Add(new Cookie(parts[0].Trim(), parts[1].Trim(), "/", ".youtube.com"));
                }
            }
            
            return cookies.Count > 0 ? cookies : null;
        }
    }
}