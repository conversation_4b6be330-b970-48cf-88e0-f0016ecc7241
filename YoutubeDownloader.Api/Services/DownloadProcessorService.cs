using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using YoutubeDownloader.Core.Downloading;
using YoutubeDownloader.Core.Tagging;
using YoutubeDownloader.Core.Utils;
using YoutubeExplode.Videos.Streams;
using YoutubeDownloader.Api.Hubs;
using Gress;

namespace YoutubeDownloader.Api.Services;

public class DownloadProcessorService : BackgroundService
{
    private readonly IDownloadQueueService _queueService;
    private readonly IStorageService _storageService;
    private readonly IConfigurationService _configService;
    private readonly IHubContext<DownloadProgressHub> _hubContext;
    private readonly ILogger<DownloadProcessorService> _logger;
    private readonly SemaphoreSlim _semaphore;

    public DownloadProcessorService(
        IDownloadQueueService queueService,
        IStorageService storageService,
        IConfigurationService configService,
        IHubContext<DownloadProgressHub> hubContext,
        ILogger<DownloadProcessorService> logger)
    {
        _queueService = queueService;
        _storageService = storageService;
        _configService = configService;
        _hubContext = hubContext;
        _logger = logger;
        _semaphore = new SemaphoreSlim(_configService.MaxParallelDownloads);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var downloadInfo = await _queueService.DequeueAsync(stoppingToken);
            if (downloadInfo == null)
                continue;

            _ = ProcessDownloadAsync(downloadInfo, stoppingToken);
        }
    }

    private async Task ProcessDownloadAsync(DownloadInfo downloadInfo, CancellationToken cancellationToken)
    {
        await _semaphore.WaitAsync(cancellationToken);
        
        try
        {
            downloadInfo.Status = DownloadStatus.Downloading;
            downloadInfo.StartedAt = DateTime.UtcNow;
            downloadInfo.CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            await NotifyProgressAsync(downloadInfo);

            var downloader = new VideoDownloader(_configService.GetAuthCookies() ?? []);
            var youtube = new YoutubeExplode.YoutubeClient(Http.Client, _configService.GetAuthCookies() ?? []);
            
            var video = await youtube.Videos.GetAsync(downloadInfo.VideoId, downloadInfo.CancellationTokenSource.Token);
            
            var container = new Container(downloadInfo.Request.Container);
            var preference = new VideoDownloadPreference(container, downloadInfo.Request.QualityPreference);
            
            var downloadOption = await downloader.GetBestDownloadOptionAsync(
                video.Id,
                preference,
                downloadInfo.Request.IncludeLanguageSpecificAudioStreams,
                downloadInfo.CancellationTokenSource.Token);

            var fileNameTemplate = downloadInfo.Request.FileNameTemplate ?? _configService.FileNameTemplate;
            var fileName = FileNameTemplate.Apply(fileNameTemplate, video, container);
            var filePath = _storageService.GenerateFilePath(fileName);

            downloadInfo.FileName = fileName;
            downloadInfo.FilePath = filePath;

            var progress = new Progress<double>(p =>
            {
                downloadInfo.Progress.Report(new Percentage(p));
                _ = NotifyProgressAsync(downloadInfo);
            });

            await downloader.DownloadVideoAsync(
                filePath,
                video,
                downloadOption,
                downloadInfo.Request.IncludeSubtitles,
                progress.ToPercentageBased(),
                downloadInfo.CancellationTokenSource.Token);

            if (downloadInfo.Request.InjectTags)
            {
                try
                {
                    var tagInjector = new MediaTagInjector();
                    await tagInjector.InjectTagsAsync(filePath, video, downloadInfo.CancellationTokenSource.Token);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to inject tags for download {DownloadId}", downloadInfo.Id);
                }
            }

            downloadInfo.FileSizeBytes = await _storageService.GetFileSizeAsync(filePath);
            downloadInfo.Status = DownloadStatus.Completed;
            downloadInfo.CompletedAt = DateTime.UtcNow;
            
            _logger.LogInformation("Download completed: {DownloadId} - {FileName}", downloadInfo.Id, fileName);
        }
        catch (OperationCanceledException)
        {
            downloadInfo.Status = DownloadStatus.Cancelled;
            downloadInfo.ErrorMessage = "Download was cancelled";
            _logger.LogInformation("Download cancelled: {DownloadId}", downloadInfo.Id);
        }
        catch (Exception ex)
        {
            downloadInfo.Status = DownloadStatus.Failed;
            downloadInfo.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Download failed: {DownloadId}", downloadInfo.Id);
            
            if (!string.IsNullOrEmpty(downloadInfo.FilePath))
            {
                await _storageService.DeleteFileAsync(downloadInfo.FilePath);
            }
        }
        finally
        {
            _semaphore.Release();
            await NotifyProgressAsync(downloadInfo);
        }
    }

    private async Task NotifyProgressAsync(DownloadInfo downloadInfo)
    {
        await _hubContext.Clients.All.SendAsync("DownloadProgress", new
        {
            downloadInfo.Id,
            Status = downloadInfo.Status.ToString(),
            Progress = downloadInfo.Progress.Current.Fraction,
            downloadInfo.VideoTitle,
            downloadInfo.ErrorMessage
        });
    }
}

public static class ProgressExtensions
{
    public static IProgress<Percentage> ToPercentageBased(this IProgress<double> progress)
    {
        return new Progress<Percentage>(p => progress.Report(p.Fraction));
    }
}