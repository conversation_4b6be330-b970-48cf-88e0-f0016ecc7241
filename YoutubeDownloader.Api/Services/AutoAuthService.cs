using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace YoutubeDownloader.Api.Services;

/// <summary>
/// Service that automatically authenticates with YouTube on startup if credentials are provided
/// </summary>
public class AutoAuthService : IHostedService
{
    private readonly IBrowserAuthService _browserAuthService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AutoAuthService> _logger;

    public AutoAuthService(
        IBrowserAuthService browserAuthService,
        IConfiguration configuration,
        ILogger<AutoAuthService> logger)
    {
        _browserAuthService = browserAuthService;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // Check if we already have cookies
        var existingCookies = _configuration["AUTH_COOKIES"];
        if (!string.IsNullOrEmpty(existingCookies))
        {
            _logger.LogInformation("Authentication cookies already configured");
            return;
        }

        // Check if credentials are provided via environment variables
        var username = _configuration["YOUTUBE_USERNAME"];
        var password = _configuration["YOUTUBE_PASSWORD"];

        if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
        {
            // Full authentication with credentials
            _logger.LogInformation("YouTube credentials provided, attempting full authentication...");
            
            try
            {
                var cookies = await _browserAuthService.AuthenticateAsync(username, password, cancellationToken);
                
                if (cookies != null && cookies.Count > 0)
                {
                    _logger.LogInformation("Successfully authenticated with YouTube! Obtained {Count} cookies", cookies.Count);
                    await SaveCookiesAsync(cookies, cancellationToken);
                    return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to authenticate with credentials");
            }
        }

        // Try to get basic cookies by just visiting YouTube (no login)
        _logger.LogInformation("Attempting to obtain basic YouTube cookies without login...");
        
        try
        {
            var cookies = await _browserAuthService.GetCookiesFromSessionAsync(cancellationToken);
            
            if (cookies == null || cookies.Count == 0)
            {
                // If that doesn't work, try opening YouTube fresh
                _logger.LogInformation("Opening YouTube to obtain cookies...");
                cookies = await GetBasicYouTubeCookiesAsync(cancellationToken);
            }
            
            if (cookies != null && cookies.Count > 0)
            {
                _logger.LogInformation("Obtained {Count} basic cookies from YouTube", cookies.Count);
                await SaveCookiesAsync(cookies, cancellationToken);
                _logger.LogInformation("Basic cookies may work for some public videos");
                _logger.LogInformation("For full access, provide YOUTUBE_USERNAME and YOUTUBE_PASSWORD environment variables");
            }
            else
            {
                _logger.LogWarning("Could not obtain YouTube cookies");
                _logger.LogWarning("Downloads may fail. To fix:");
                _logger.LogWarning("1. Set YOUTUBE_USERNAME and YOUTUBE_PASSWORD environment variables");
                _logger.LogWarning("2. Or use POST /api/auth/login endpoint");
            }
        }
        catch (NotSupportedException)
        {
            _logger.LogWarning("Browser authentication not available in this build");
            _logger.LogWarning("Please use Dockerfile.working for full authentication support");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to obtain YouTube cookies");
        }
    }

    private async Task<System.Collections.Generic.IReadOnlyList<System.Net.Cookie>?> GetBasicYouTubeCookiesAsync(CancellationToken cancellationToken)
    {
        // This is a fallback to get basic cookies without login
        // The BrowserAuthService will handle opening YouTube in a headless browser
        try
        {
            // Just visit YouTube homepage to get basic cookies
            return await _browserAuthService.GetCookiesFromSessionAsync(cancellationToken);
        }
        catch
        {
            return null;
        }
    }

    private async Task SaveCookiesAsync(System.Collections.Generic.IReadOnlyList<System.Net.Cookie> cookies, CancellationToken cancellationToken)
    {
        try
        {
            var cookiesJson = System.Text.Json.JsonSerializer.Serialize(cookies);
            var configPath = _configuration["CONFIG_PATH"] ?? "/app/config";
            var cookieFile = System.IO.Path.Combine(configPath, "cookies.json");
            
            System.IO.Directory.CreateDirectory(configPath);
            await System.IO.File.WriteAllTextAsync(cookieFile, cookiesJson, cancellationToken);
            _logger.LogInformation("Cookies saved to {Path}", cookieFile);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not save cookies to file");
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}