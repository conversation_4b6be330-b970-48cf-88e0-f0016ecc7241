using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using YoutubeDownloader.Api.Models.Requests;
using YoutubeDownloader.Api.Models.Responses;
using YoutubeDownloader.Core.Downloading;
using YoutubeDownloader.Core.Resolving;
using YoutubeDownloader.Core.Tagging;
using YoutubeDownloader.Core.Utils;
using YoutubeDownloader.Api.Utils;
using YoutubeExplode;
using YoutubeExplode.Common;
using YoutubeExplode.Videos;
using YoutubeExplode.Videos.Streams;
using Gress;

namespace YoutubeDownloader.Api.Services;

public class DownloadService : IDownloadService
{
    private readonly IDownloadQueueService _queueService;
    private readonly IStorageService _storageService;
    private readonly IConfigurationService _configService;
    private readonly ILogger<DownloadService> _logger;
    private readonly ConcurrentDictionary<string, DownloadInfo> _downloads = new();

    public DownloadService(
        IDownloadQueueService queueService,
        IStorageService storageService,
        IConfigurationService configService,
        ILogger<DownloadService> logger)
    {
        _queueService = queueService;
        _storageService = storageService;
        _configService = configService;
        _logger = logger;
    }

    public async Task<IReadOnlyList<DownloadOptionResponse>> GetDownloadOptionsAsync(
        string videoId,
        CancellationToken cancellationToken = default)
    {
        var downloader = new VideoDownloader(_configService.GetAuthCookies() ?? []);
        var options = await downloader.GetDownloadOptionsAsync(
            videoId,
            _configService.IncludeLanguageSpecificAudioStreams,
            cancellationToken);

        return options.Select(opt => new DownloadOptionResponse
        {
            Container = opt.Container.Name,
            IsAudioOnly = opt.IsAudioOnly,
            VideoQuality = opt.VideoQuality?.Label,
            AudioBitrate = opt.StreamInfos.OfType<IAudioStreamInfo>().FirstOrDefault()?.Bitrate.ToString(),
            FileSizeBytes = opt.StreamInfos.Sum(s => s.Size.Bytes)
        }).ToList();
    }

    public async Task<string> StartDownloadAsync(
        StartDownloadRequest request,
        CancellationToken cancellationToken = default)
    {
        // When we have a specific video ID, construct a proper YouTube URL
        // This ensures it's parsed as a video, not a playlist
        string query = "";
        if (!string.IsNullOrEmpty(request.Url))
        {
            query = request.Url;
        }
        else if (!string.IsNullOrEmpty(request.VideoId))
        {
            // Convert video ID to full URL to avoid ambiguity with playlist IDs
            query = $"https://www.youtube.com/watch?v={request.VideoId}";
        }
        else
        {
            throw new ArgumentException("Either URL or VideoId must be provided");
        }
        
        // Use QueryResolver to properly resolve the video, just like desktop does
        var resolver = new QueryResolver(_configService.GetAuthCookies());
        
        // Resolve the query to get the video
        var queryResult = await resolver.ResolveAsync(query, cancellationToken);
        
        if (queryResult.Videos.Count == 0)
        {
            throw new InvalidOperationException($"No video found for query: {query}");
        }
        
        if (queryResult.Videos.Count > 1)
        {
            throw new InvalidOperationException($"Multiple videos found. Please use a more specific query.");
        }
        
        var video = queryResult.Videos[0];
        var downloadId = Guid.NewGuid().ToString();
        
        var downloadInfo = new DownloadInfo
        {
            Id = downloadId,
            VideoId = video.Id,
            VideoTitle = video.Title,
            Request = request,
            Status = DownloadStatus.Queued,
            QueuedAt = DateTime.UtcNow,
            Progress = new ProgressContainer<Percentage>()
        };

        _downloads[downloadId] = downloadInfo;
        
        if (request.WaitForCompletion)
        {
            // Process download immediately and wait for completion
            await ProcessDownloadDirectlyAsync(downloadInfo, video, cancellationToken);
        }
        else
        {
            // Queue for background processing
            await _queueService.EnqueueAsync(downloadInfo);
            _logger.LogInformation("Download queued: {DownloadId} for video {VideoId}", downloadId, video.Id);
        }
        
        return downloadId;
    }
    
    private async Task ProcessDownloadDirectlyAsync(DownloadInfo downloadInfo, IVideo video, CancellationToken cancellationToken)
    {
        try
        {
            downloadInfo.Status = DownloadStatus.Downloading;
            downloadInfo.StartedAt = DateTime.UtcNow;
            downloadInfo.CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            var downloader = new VideoDownloader(_configService.GetAuthCookies() ?? []);
            
            var container = new Container(downloadInfo.Request.Container);
            var preference = new VideoDownloadPreference(container, downloadInfo.Request.QualityPreference);
            
            var downloadOption = await downloader.GetBestDownloadOptionAsync(
                video.Id,
                preference,
                downloadInfo.Request.IncludeLanguageSpecificAudioStreams,
                downloadInfo.CancellationTokenSource.Token);

            var fileNameTemplate = downloadInfo.Request.FileNameTemplate ?? _configService.FileNameTemplate;
            var fileName = FileNameTemplate.Apply(fileNameTemplate, video, container);
            var filePath = _storageService.GenerateFilePath(fileName);

            downloadInfo.FileName = fileName;
            downloadInfo.FilePath = filePath;

            var progress = new Progress<double>(p =>
            {
                downloadInfo.Progress.Report(new Percentage(p));
            });

            await downloader.DownloadVideoAsync(
                filePath,
                video,
                downloadOption,
                downloadInfo.Request.IncludeSubtitles,
                progress.ToPercentageBased(),
                downloadInfo.CancellationTokenSource.Token);

            if (downloadInfo.Request.InjectTags)
            {
                try
                {
                    var tagInjector = new MediaTagInjector();
                    await tagInjector.InjectTagsAsync(filePath, video, downloadInfo.CancellationTokenSource.Token);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to inject tags for download {DownloadId}", downloadInfo.Id);
                }
            }

            downloadInfo.FileSizeBytes = await _storageService.GetFileSizeAsync(filePath);
            downloadInfo.Status = DownloadStatus.Completed;
            downloadInfo.CompletedAt = DateTime.UtcNow;
            
            _logger.LogInformation("Direct download completed: {DownloadId} - {FileName}", downloadInfo.Id, fileName);
        }
        catch (Exception ex)
        {
            downloadInfo.Status = DownloadStatus.Failed;
            downloadInfo.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Direct download failed: {DownloadId}", downloadInfo.Id);
            
            if (!string.IsNullOrEmpty(downloadInfo.FilePath))
            {
                await _storageService.DeleteFileAsync(downloadInfo.FilePath);
            }
            throw;
        }
    }

    public Task<DownloadStatusResponse?> GetDownloadStatusAsync(string downloadId)
    {
        if (!_downloads.TryGetValue(downloadId, out var download))
            return Task.FromResult<DownloadStatusResponse?>(null);

        return Task.FromResult<DownloadStatusResponse?>(new DownloadStatusResponse
        {
            DownloadId = download.Id,
            Status = download.Status.ToString(),
            Progress = download.Progress.Current.Fraction,
            VideoTitle = download.VideoTitle,
            VideoId = download.VideoId,
            ErrorMessage = download.ErrorMessage,
            QueuedAt = download.QueuedAt,
            StartedAt = download.StartedAt,
            CompletedAt = download.CompletedAt,
            FileName = download.FileName,
            FileSizeBytes = download.FileSizeBytes
        });
    }

    public Task<IReadOnlyList<DownloadStatusResponse>> GetAllDownloadsAsync()
    {
        var downloads = _downloads.Values
            .OrderByDescending(d => d.QueuedAt)
            .Select(d => new DownloadStatusResponse
            {
                DownloadId = d.Id,
                Status = d.Status.ToString(),
                Progress = d.Progress.Current.Fraction,
                VideoTitle = d.VideoTitle,
                VideoId = d.VideoId,
                ErrorMessage = d.ErrorMessage,
                QueuedAt = d.QueuedAt,
                StartedAt = d.StartedAt,
                CompletedAt = d.CompletedAt,
                FileName = d.FileName,
                FileSizeBytes = d.FileSizeBytes
            })
            .ToList();

        return Task.FromResult<IReadOnlyList<DownloadStatusResponse>>(downloads);
    }

    public async Task<bool> CancelDownloadAsync(string downloadId)
    {
        if (!_downloads.TryGetValue(downloadId, out var download))
            return false;

        download.CancellationTokenSource?.Cancel();
        download.Status = DownloadStatus.Cancelled;
        
        _logger.LogInformation("Download cancelled: {DownloadId}", downloadId);
        
        return await Task.FromResult(true);
    }

    public async Task<(Stream? stream, string? fileName, string? contentType)?> GetDownloadFileAsync(string downloadId)
    {
        if (!_downloads.TryGetValue(downloadId, out var download))
            return null;

        if (download.Status != DownloadStatus.Completed || string.IsNullOrEmpty(download.FilePath))
            return null;

        var stream = await _storageService.GetFileStreamAsync(download.FilePath);
        if (stream == null)
            return null;

        var contentType = Path.GetExtension(download.FileName)?.ToLower() switch
        {
            ".mp4" => "video/mp4",
            ".webm" => "video/webm",
            ".mp3" => "audio/mpeg",
            ".m4a" => "audio/mp4",
            _ => "application/octet-stream"
        };

        return (stream, download.FileName, contentType);
    }
}

public class DownloadInfo
{
    public required string Id { get; set; }
    public required string VideoId { get; set; }
    public string? VideoTitle { get; set; }
    public required StartDownloadRequest Request { get; set; }
    public DownloadStatus Status { get; set; }
    public DateTime QueuedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? FilePath { get; set; }
    public string? FileName { get; set; }
    public long? FileSizeBytes { get; set; }
    public string? ErrorMessage { get; set; }
    public ProgressContainer<Percentage> Progress { get; set; } = new();
    public CancellationTokenSource? CancellationTokenSource { get; set; }
}

public enum DownloadStatus
{
    Queued,
    Downloading,
    Completed,
    Failed,
    Cancelled
}