using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using YoutubeDownloader.Api.Models.Requests;
using YoutubeDownloader.Api.Models.Responses;
using YoutubeDownloader.Core.Downloading;

namespace YoutubeDownloader.Api.Services;

public interface IDownloadService
{
    Task<IReadOnlyList<DownloadOptionResponse>> GetDownloadOptionsAsync(
        string videoId,
        CancellationToken cancellationToken = default);
    
    Task<string> StartDownloadAsync(
        StartDownloadRequest request,
        CancellationToken cancellationToken = default);
    
    Task<DownloadStatusResponse?> GetDownloadStatusAsync(string downloadId);
    
    Task<IReadOnlyList<DownloadStatusResponse>> GetAllDownloadsAsync();
    
    Task<bool> CancelDownloadAsync(string downloadId);
    
    Task<(Stream? stream, string? fileName, string? contentType)?> GetDownloadFileAsync(string downloadId);
}