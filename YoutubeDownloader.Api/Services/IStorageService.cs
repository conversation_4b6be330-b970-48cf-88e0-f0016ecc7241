using System;
using System.IO;
using System.Threading.Tasks;

namespace YoutubeDownloader.Api.Services;

public interface IStorageService
{
    string GetDownloadPath();
    string GenerateFilePath(string fileName);
    Task<Stream?> GetFileStreamAsync(string filePath);
    Task DeleteFileAsync(string filePath);
    Task<long> GetFileSizeAsync(string filePath);
    Task CleanupOldFilesAsync(TimeSpan maxAge);
}