using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;

namespace YoutubeDownloader.Api.Services;

public interface IBrowserAuthService
{
    Task<IReadOnlyList<System.Net.Cookie>?> AuthenticateAsync(string username, string password, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<System.Net.Cookie>?> GetCookiesFromSessionAsync(CancellationToken cancellationToken = default);
}

public class BrowserAuthService : IBrowserAuthService, IAsyncDisposable
{
    private readonly ILogger<BrowserAuthService> _logger;
    private readonly IConfiguration _configuration;
    private IPlaywright? _playwright;
    private IBrowser? _browser;
    private readonly SemaphoreSlim _semaphore = new(1, 1);
    
    private const string HomePageUrl = "https://www.youtube.com";
    private static readonly string LoginPageUrl = 
        $"https://accounts.google.com/ServiceLogin?continue={Uri.EscapeDataString(HomePageUrl)}";

    public BrowserAuthService(ILogger<BrowserAuthService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    private async Task InitializeBrowserAsync()
    {
        if (_browser != null && _browser.IsConnected)
            return;

        _logger.LogInformation("Initializing headless browser...");
        
        _playwright = await Playwright.CreateAsync();
        
        // Use Chromium for better compatibility with Google login
        _browser = await _playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
        {
            Headless = _configuration.GetValue<bool>("BROWSER_HEADLESS", true),
            Args = new[]
            {
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-gpu",
                "--disable-web-security",
                "--disable-features=IsolateOrigins,site-per-process"
            }
        });
        
        _logger.LogInformation("Browser initialized successfully");
    }

    public async Task<IReadOnlyList<System.Net.Cookie>?> AuthenticateAsync(
        string username, 
        string password, 
        CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            await InitializeBrowserAsync();
            
            _logger.LogInformation("Starting authentication for user: {Username}", username);
            
            // Create a new browser context (like incognito mode)
            var context = await _browser!.NewContextAsync(new BrowserNewContextOptions
            {
                UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                ViewportSize = new ViewportSize { Width = 1920, Height = 1080 },
                Locale = "en-US"
            });

            var page = await context.NewPageAsync();
            
            try
            {
                // Navigate to Google login
                _logger.LogDebug("Navigating to login page...");
                await page.GotoAsync(LoginPageUrl, new PageGotoOptions 
                { 
                    WaitUntil = WaitUntilState.NetworkIdle,
                    Timeout = 60000 
                });
                
                // Enter email
                _logger.LogDebug("Entering email...");
                await page.FillAsync("input[type='email']", username);
                await page.PressAsync("input[type='email']", "Enter");
                
                // Wait for password field
                await page.WaitForSelectorAsync("input[type='password']", new PageWaitForSelectorOptions 
                { 
                    State = WaitForSelectorState.Visible,
                    Timeout = 30000 
                });
                
                // Small delay to appear more human-like
                await Task.Delay(1000, cancellationToken);
                
                // Enter password
                _logger.LogDebug("Entering password...");
                await page.FillAsync("input[type='password']", password);
                await page.PressAsync("input[type='password']", "Enter");
                
                // Wait for redirect to YouTube
                _logger.LogDebug("Waiting for authentication to complete...");
                await page.WaitForURLAsync(url => url.StartsWith(HomePageUrl), new PageWaitForURLOptions 
                { 
                    Timeout = 60000 
                });
                
                _logger.LogInformation("Authentication successful, extracting cookies...");
                
                // Get cookies
                var browserCookies = await context.CookiesAsync(new[] { HomePageUrl });
                
                // Convert to System.Net.Cookie
                var cookies = browserCookies
                    .Where(c => c.Domain.Contains("youtube.com") || c.Domain.Contains("google.com"))
                    .Select(c => new System.Net.Cookie(
                        c.Name,
                        c.Value,
                        c.Path,
                        c.Domain.TrimStart('.')
                    ))
                    .ToList();
                
                _logger.LogInformation("Extracted {Count} cookies", cookies.Count);
                
                // Save cookies to configuration
                SaveCookiesToConfig(cookies);
                
                return cookies;
            }
            finally
            {
                await context.CloseAsync();
            }
        }
        catch (TimeoutException ex)
        {
            _logger.LogError(ex, "Authentication timeout - possibly requires captcha or 2FA");
            throw new InvalidOperationException("Authentication failed - manual intervention may be required (captcha/2FA)", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authentication failed");
            throw;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task<IReadOnlyList<System.Net.Cookie>?> GetCookiesFromSessionAsync(CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            await InitializeBrowserAsync();
            
            _logger.LogInformation("Opening YouTube to obtain cookies...");
            
            var context = await _browser!.NewContextAsync(new BrowserNewContextOptions
            {
                UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                StorageStatePath = _configuration["BROWSER_SESSION_PATH"] ?? "/app/browser-session",
                AcceptDownloads = false,
                ViewportSize = new ViewportSize { Width = 1920, Height = 1080 }
            });

            var page = await context.NewPageAsync();
            
            try
            {
                // Navigate to YouTube
                _logger.LogDebug("Navigating to YouTube homepage...");
                await page.GotoAsync(HomePageUrl, new PageGotoOptions 
                { 
                    WaitUntil = WaitUntilState.NetworkIdle,
                    Timeout = 30000 
                });
                
                // Wait a bit for cookies to be set
                await Task.Delay(2000, cancellationToken);
                
                // Accept cookies consent if prompted (for EU users)
                try
                {
                    var consentButton = page.Locator("button:has-text('Accept all')").First;
                    if (await consentButton.IsVisibleAsync())
                    {
                        await consentButton.ClickAsync();
                        await Task.Delay(1000, cancellationToken);
                    }
                }
                catch
                {
                    // Consent dialog might not appear
                }
                
                _logger.LogInformation("Active session found, extracting cookies...");
                
                // Get cookies
                var browserCookies = await context.CookiesAsync(new[] { HomePageUrl });
                
                // Convert to System.Net.Cookie
                var cookies = browserCookies
                    .Where(c => c.Domain.Contains("youtube.com") || c.Domain.Contains("google.com"))
                    .Select(c => new System.Net.Cookie(
                        c.Name,
                        c.Value,
                        c.Path,
                        c.Domain.TrimStart('.')
                    ))
                    .ToList();
                
                _logger.LogInformation("Extracted {Count} cookies from session", cookies.Count);
                
                // Save cookies to configuration
                SaveCookiesToConfig(cookies);
                
                // Save session state for reuse
                await context.StorageStateAsync(new BrowserContextStorageStateOptions
                {
                    Path = _configuration["BROWSER_SESSION_PATH"] ?? "/app/browser-session"
                });
                
                return cookies;
            }
            finally
            {
                await context.CloseAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cookies from session");
            return null;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private void SaveCookiesToConfig(IReadOnlyList<System.Net.Cookie> cookies)
    {
        try
        {
            var cookieJson = System.Text.Json.JsonSerializer.Serialize(
                cookies.Select(c => new
                {
                    c.Name,
                    c.Value,
                    c.Domain,
                    c.Path
                })
            );
            
            _configuration["AUTH_COOKIES"] = cookieJson;
            _logger.LogDebug("Cookies saved to configuration");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save cookies to configuration");
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_browser != null)
        {
            await _browser.CloseAsync();
            _browser = null;
        }
        
        _playwright?.Dispose();
        _playwright = null;
        
        _semaphore?.Dispose();
    }
}