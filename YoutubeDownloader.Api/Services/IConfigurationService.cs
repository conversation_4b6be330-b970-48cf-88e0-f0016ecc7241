using System.Collections.Generic;
using System.Net;
using YoutubeDownloader.Core.Downloading;

namespace YoutubeDownloader.Api.Services;

public interface IConfigurationService
{
    int MaxParallelDownloads { get; }
    VideoQualityPreference DefaultQualityPreference { get; }
    string DefaultContainer { get; }
    bool IncludeSubtitles { get; }
    bool InjectTags { get; }
    bool IncludeLanguageSpecificAudioStreams { get; }
    string FileNameTemplate { get; }
    bool SkipExistingFiles { get; }
    string FFmpegPath { get; }
    IReadOnlyList<Cookie>? GetAuthCookies();
}