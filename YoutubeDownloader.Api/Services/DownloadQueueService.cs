using System;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace YoutubeDownloader.Api.Services;

public class DownloadQueueService : IDownloadQueueService
{
    private readonly Channel<DownloadInfo> _queue;
    private readonly ILogger<DownloadQueueService> _logger;

    public DownloadQueueService(ILogger<DownloadQueueService> logger)
    {
        _logger = logger;
        _queue = Channel.CreateUnbounded<DownloadInfo>();
    }

    public async Task EnqueueAsync(DownloadInfo downloadInfo)
    {
        await _queue.Writer.WriteAsync(downloadInfo);
        _logger.LogDebug("Download enqueued: {DownloadId}", downloadInfo.Id);
    }

    public async Task<DownloadInfo?> DequeueAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _queue.Reader.ReadAsync(cancellationToken);
        }
        catch (OperationCanceledException)
        {
            return null;
        }
    }

    public int GetQueueLength()
    {
        return _queue.Reader.Count;
    }
}