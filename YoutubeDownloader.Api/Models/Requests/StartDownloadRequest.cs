using YoutubeDownloader.Core.Downloading;

namespace YoutubeDownloader.Api.Models.Requests;

public class StartDownloadRequest
{
    // Accept either URL or VideoId
    public string? Url { get; set; }
    public string? VideoId { get; set; }
    
    // Download options with defaults
    public VideoQualityPreference QualityPreference { get; set; } = VideoQualityPreference.Highest;
    public string Container { get; set; } = "mp4"; // Default to mp4 for video
    public bool IncludeSubtitles { get; set; } = true; // Default true for mp4
    public bool InjectTags { get; set; } = true;
    public bool IncludeLanguageSpecificAudioStreams { get; set; } = true;
    public string? FileNameTemplate { get; set; }
    
    // Control whether to queue or download directly
    public bool WaitForCompletion { get; set; } = false; // If true, waits for download to complete
    public bool ReturnDownloadUrl { get; set; } = false; // If true, returns direct download URL
}