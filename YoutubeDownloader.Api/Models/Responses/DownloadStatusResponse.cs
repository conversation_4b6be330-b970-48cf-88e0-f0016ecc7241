using System;

namespace YoutubeDownloader.Api.Models.Responses;

public class DownloadStatusResponse
{
    public required string DownloadId { get; set; }
    public required string Status { get; set; }
    public double Progress { get; set; }
    public string? VideoTitle { get; set; }
    public string? VideoId { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime QueuedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? FileName { get; set; }
    public long? FileSizeBytes { get; set; }
}