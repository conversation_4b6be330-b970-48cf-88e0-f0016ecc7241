# YoutubeDownloader API

A RESTful HTTP API for downloading YouTube videos, built on top of the YoutubeDownloader.Core library.

## Features

- 🎥 Download YouTube videos in various qualities and formats
- 📋 Support for playlists and channels
- 🔍 Search functionality
- 📊 Real-time download progress via SignalR
- 🏷️ Automatic metadata tagging
- 📝 Subtitle support
- 🔄 Queue management with parallel downloads
- 🧹 Automatic cleanup of old files
- 📖 Swagger/OpenAPI documentation

## Prerequisites

- .NET 9.0 Runtime
- FFmpeg (must be installed and accessible in PATH or specified via configuration)

## Configuration

The API can be configured using environment variables or appsettings.json:

| Variable | Default | Description |
|----------|---------|-------------|
| `DOWNLOAD_PATH` | `downloads` | Directory where files are saved |
| `MAX_PARALLEL_DOWNLOADS` | `2` | Number of concurrent downloads |
| `DEFAULT_QUALITY` | `Highest` | Default video quality preference |
| `DEFAULT_FORMAT` | `mp4` | Default container format |
| `FFMPEG_PATH` | `ffmpeg` | Path to FFmpeg executable |
| `ENABLE_SUBTITLES` | `true` | Include subtitles in downloads |
| `ENABLE_TAGS` | `true` | Inject metadata tags |
| `CLEANUP_ENABLED` | `true` | Enable automatic file cleanup |
| `CLEANUP_INTERVAL_HOURS` | `24` | Cleanup check interval |
| `MAX_FILE_RETENTION_DAYS` | `7` | File retention period |
| `EnableSwagger` | `true` | Enable Swagger UI |

## Running the API

### Development Mode

```bash
cd YoutubeDownloader.Api
dotnet run
```

The API will be available at `http://localhost:5000` (or the port specified in launchSettings.json).

### Production Mode

```bash
dotnet publish -c Release
cd bin/Release/net9.0/publish
dotnet YoutubeDownloader.Api.dll
```

## API Endpoints

### Authentication (For Restricted Videos)

Some YouTube videos require authentication to access. You can configure authentication cookies to access these videos.

**POST** `/api/auth/set-cookies`

Set YouTube authentication cookies for accessing restricted content.

```json
{
  "cookies": "cookie1=value1; cookie2=value2"
}
```

Or in JSON format:
```json
{
  "cookies": "[{\"Name\":\"__Secure-1PAPISID\",\"Value\":\"...\",\"Domain\":\".youtube.com\",\"Path\":\"/\"}]"
}
```

**GET** `/api/auth/status`

Check if authentication cookies are configured.

Response:
```json
{
  "isAuthenticated": true,
  "message": "Cookies are configured"
}
```

### Query Resolution

**POST** `/api/query/resolve`

Resolves a YouTube URL or search query.

```json
{
  "query": "https://youtube.com/watch?v=VIDEO_ID"
}
```

Response:
```json
{
  "kind": "Video",
  "title": "Video Title",
  "videos": [
    {
      "id": "VIDEO_ID",
      "title": "Video Title",
      "author": "Channel Name",
      "duration": "00:10:30",
      "thumbnailUrl": "https://..."
    }
  ]
}
```

### Download Options

**GET** `/api/download/options/{videoId}`

Get available download options for a video.

Response:
```json
[
  {
    "container": "mp4",
    "isAudioOnly": false,
    "videoQuality": "1080p60",
    "fileSizeBytes": 104857600
  }
]
```

### Start Download - Universal Endpoint

**POST** `/api/download/start`

A flexible endpoint that handles all download scenarios. Can queue downloads for background processing or wait for completion and return a download URL.

#### Request Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `url` | string | - | YouTube URL (e.g., `https://youtube.com/watch?v=VIDEO_ID`) |
| `videoId` | string | - | YouTube video ID (alternative to URL) |
| `container` | string | `"mp4"` | Output format: `mp4`, `webm`, `mp3`, `m4a`, `ogg` |
| `qualityPreference` | string | `"Highest"` | Quality: `Highest`, `UpTo1080p`, `UpTo720p`, `UpTo480p`, `Lowest` |
| `includeSubtitles` | bool | `true` | Include subtitles in video downloads |
| `injectTags` | bool | `true` | Add metadata tags to the file |
| `includeLanguageSpecificAudioStreams` | bool | `true` | Include language-specific audio tracks |
| `fileNameTemplate` | string | `"$title"` | Template for filename (see File Name Templates section) |
| `waitForCompletion` | bool | `false` | Wait for download to complete before responding |
| `returnDownloadUrl` | bool | `false` | Return direct download URL in response |

**Note:** Either `url` or `videoId` must be provided. All other parameters are optional.

#### Usage Examples

##### 1. Minimal Request (Download MP4 in background)
```json
{
  "url": "https://youtube.com/watch?v=VIDEO_ID"
}
```
Response:
```json
{
  "downloadId": "abc123",
  "status": "queued"
}
```

##### 2. Direct Download with URL (Wait for completion)
```json
{
  "url": "https://youtube.com/watch?v=VIDEO_ID",
  "waitForCompletion": true,
  "returnDownloadUrl": true
}
```
Response:
```json
{
  "downloadId": "abc123",
  "status": "Completed",
  "downloadUrl": "http://localhost:5000/api/download/file/abc123",
  "fileName": "Video Title.mp4",
  "fileSizeBytes": 104857600
}
```

##### 3. Download as MP3 Audio
```json
{
  "url": "https://youtube.com/watch?v=VIDEO_ID",
  "container": "mp3",
  "includeSubtitles": false,
  "waitForCompletion": true,
  "returnDownloadUrl": true
}
```

##### 4. Download with Specific Quality
```json
{
  "videoId": "VIDEO_ID",
  "qualityPreference": "UpTo720p",
  "container": "mp4"
}
```

##### 5. Custom Filename Template
```json
{
  "url": "https://youtube.com/watch?v=VIDEO_ID",
  "fileNameTemplate": "$author - $title ($uploadDate)",
  "waitForCompletion": true
}
```

#### Response Formats

**When Queued (default):**
```json
{
  "downloadId": "unique-guid",
  "status": "queued"
}
```

**When Waiting for Completion:**
```json
{
  "downloadId": "unique-guid",
  "status": "Completed",  // or "Failed"
  "fileName": "Video Title.mp4",
  "fileSizeBytes": 104857600,
  "error": null  // Contains error message if failed
}
```

**When Requesting Download URL:**
```json
{
  "downloadId": "unique-guid",
  "status": "Completed",
  "downloadUrl": "http://localhost:5000/api/download/file/unique-guid",
  "fileName": "Video Title.mp4",
  "fileSizeBytes": 104857600
}
```

### Download Status

**GET** `/api/download/status/{downloadId}`

Get the status of a specific download.

Response:
```json
{
  "downloadId": "guid",
  "status": "Downloading",
  "progress": 0.45,
  "videoTitle": "Video Title",
  "videoId": "VIDEO_ID",
  "queuedAt": "2024-01-01T00:00:00Z",
  "startedAt": "2024-01-01T00:00:10Z",
  "fileName": "Video Title.mp4",
  "fileSizeBytes": 104857600
}
```

### List Downloads

**GET** `/api/download/list`

Get all downloads and their statuses.

### Download File

**GET** `/api/download/file/{downloadId}`

Download the completed file.

### Cancel Download

**DELETE** `/api/download/{downloadId}`

Cancel a queued or in-progress download.

### Health Check

**GET** `/api/health`

Get API health status.

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "checks": {
    "ffmpeg": true,
    "downloadPath": true,
    "queueLength": 0
  }
}
```

## Real-time Progress Updates

Connect to the SignalR hub at `/hubs/progress` to receive real-time download progress updates.

### JavaScript Client Example

```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("http://localhost:5000/hubs/progress")
    .build();

connection.on("DownloadProgress", (update) => {
    console.log(`Download ${update.id}: ${update.status} - ${update.progress * 100}%`);
});

await connection.start();

// Subscribe to specific download
await connection.invoke("SubscribeToDownload", downloadId);
```

## Swagger Documentation

When Swagger is enabled, interactive API documentation is available at:
- Swagger UI: `http://localhost:5000/swagger`
- OpenAPI JSON: `http://localhost:5000/swagger/v1/swagger.json`

## File Name Templates

The following variables can be used in file name templates:

- `$title` - Video title
- `$author` - Channel/author name
- `$id` - Video ID
- `$uploadDate` - Upload date

Example: `$author - $title ($id)`

## Quality Preferences

Available quality preferences:
- `Highest` - Best available quality
- `UpTo1080p` - Maximum 1080p
- `UpTo720p` - Maximum 720p
- `UpTo480p` - Maximum 480p
- `Lowest` - Lowest available quality

## Container Formats

Supported container formats:
- `mp4` - MP4 video
- `webm` - WebM video
- `mp3` - MP3 audio only
- `m4a` - M4A audio only
- `ogg` - OGG audio only

## Error Handling

The API returns appropriate HTTP status codes:
- `200 OK` - Successful request
- `400 Bad Request` - Invalid request parameters
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error
- `503 Service Unavailable` - Service temporarily unavailable (e.g., FFmpeg missing)

Error responses include a JSON body with error details:
```json
{
  "error": {
    "message": "Error description",
    "type": "ExceptionType",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

## Common Usage Examples

### Simple MP4 Download (Queue for background)
```bash
curl -X POST http://localhost:5000/api/download/start \
  -H "Content-Type: application/json" \
  -d '{"url": "https://youtube.com/watch?v=dQw4w9WgXcQ"}'
```

### Direct Download MP4 (Wait and get URL)
```bash
curl -X POST http://localhost:5000/api/download/start \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://youtube.com/watch?v=dQw4w9WgXcQ",
    "waitForCompletion": true,
    "returnDownloadUrl": true
  }'
```

### Download as MP3 Audio
```bash
curl -X POST http://localhost:5000/api/download/start \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://youtube.com/watch?v=dQw4w9WgXcQ",
    "container": "mp3",
    "includeSubtitles": false,
    "waitForCompletion": true,
    "returnDownloadUrl": true
  }'
```

### Download with Custom Quality (720p)
```bash
curl -X POST http://localhost:5000/api/download/start \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://youtube.com/watch?v=dQw4w9WgXcQ",
    "qualityPreference": "UpTo720p",
    "container": "mp4"
  }'
```

### Using Video ID Instead of URL
```bash
curl -X POST http://localhost:5000/api/download/start \
  -H "Content-Type: application/json" \
  -d '{
    "videoId": "dQw4w9WgXcQ",
    "container": "mp4"
  }'
```

## Security Considerations

- The API does not implement authentication by default
- Consider adding API key authentication for production use
- Implement rate limiting to prevent abuse
- Configure CORS appropriately for your use case
- Set reasonable limits for parallel downloads and file retention

## Deployment

See the main CLAUDE.md file for Docker deployment instructions.