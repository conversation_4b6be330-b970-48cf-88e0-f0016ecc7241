using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using YoutubeDownloader.Api.Middleware;
using YoutubeDownloader.Api.Services;
using YoutubeDownloader.Api.Hubs;

var builder = WebApplication.CreateBuilder(args);

Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .WriteTo.Console()
    .WriteTo.File("logs/api-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "YoutubeDownloader API", Version = "v1" });
});

builder.Services.AddSignalR();
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.AddSingleton<IDownloadQueueService, DownloadQueueService>();
builder.Services.AddSingleton<IDownloadService, DownloadService>();
builder.Services.AddSingleton<IStorageService, LocalStorageService>();
builder.Services.AddSingleton<IConfigurationService, ConfigurationService>();

// Check if Playwright is available (full Docker image)
bool hasPlaywright = false;
try
{
    // Try to load Playwright assembly
    var playwrightAssembly = System.Reflection.Assembly.Load("Microsoft.Playwright");
    hasPlaywright = playwrightAssembly != null;
}
catch
{
    // Playwright not available
}

if (hasPlaywright)
{
    builder.Services.AddSingleton<IBrowserAuthService, BrowserAuthService>();
    builder.Configuration["ENABLE_BROWSER_AUTH"] = "true";
}
else
{
    builder.Services.AddSingleton<IBrowserAuthService, StubBrowserAuthService>();
    builder.Configuration["ENABLE_BROWSER_AUTH"] = "false";
}

// Only add AutoAuthService if browser auth is available
if (hasPlaywright)
{
    builder.Services.AddHostedService<AutoAuthService>();
}
builder.Services.AddHostedService<DownloadProcessorService>();
builder.Services.AddHostedService<CleanupService>();

builder.Services.AddHttpContextAccessor();
builder.Services.AddMemoryCache();

var app = builder.Build();

if (app.Environment.IsDevelopment() || builder.Configuration.GetValue<bool>("EnableSwagger"))
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseSerilogRequestLogging();
app.UseMiddleware<ErrorHandlingMiddleware>();
app.UseCors();
app.UseRouting();
app.MapControllers();
app.MapHub<DownloadProgressHub>("/hubs/progress");

app.MapGet("/", () => Results.Redirect("/swagger"));

try
{
    Log.Information("Starting YoutubeDownloader API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}