using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using YoutubeDownloader.Api.Services;

namespace YoutubeDownloader.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthController> _logger;
    private readonly IBrowserAuthService _browserAuthService;

    public AuthController(
        IConfiguration configuration, 
        ILogger<AuthController> logger,
        IBrowserAuthService browserAuthService)
    {
        _configuration = configuration;
        _logger = logger;
        _browserAuthService = browserAuthService;
    }

    [HttpPost("set-cookies")]
    public IActionResult SetCookies([FromBody] SetCookiesRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Cookies))
            {
                _configuration["AUTH_COOKIES"] = "";
                _logger.LogInformation("Auth cookies cleared");
                return Ok(new { message = "Cookies cleared" });
            }

            // Validate that the cookies can be parsed
            try
            {
                var testCookies = System.Text.Json.JsonSerializer.Deserialize<List<Cookie>>(request.Cookies);
                if (testCookies == null || testCookies.Count == 0)
                {
                    return BadRequest(new { error = "Invalid cookie format" });
                }
            }
            catch
            {
                // Try simple format
                var pairs = request.Cookies.Split(';', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                if (pairs.Length == 0)
                {
                    return BadRequest(new { error = "Invalid cookie format" });
                }
            }

            _configuration["AUTH_COOKIES"] = request.Cookies;
            _logger.LogInformation("Auth cookies updated");
            
            return Ok(new { message = "Cookies set successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cookies");
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpGet("status")]
    public IActionResult GetAuthStatus()
    {
        var cookieString = _configuration["AUTH_COOKIES"];
        var isAuthenticated = !string.IsNullOrEmpty(cookieString);
        
        return Ok(new 
        { 
            isAuthenticated,
            message = isAuthenticated ? "Cookies are configured" : "No cookies configured"
        });
    }

    [HttpPost("login")]
    public async Task<IActionResult> LoginWithBrowser(
        [FromBody] BrowserLoginRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
            {
                return BadRequest(new { error = "Username and password are required" });
            }

            _logger.LogInformation("Starting browser-based authentication for user: {Username}", request.Username);
            
            var cookies = await _browserAuthService.AuthenticateAsync(
                request.Username, 
                request.Password, 
                cancellationToken);
            
            if (cookies == null || cookies.Count == 0)
            {
                return Unauthorized(new { error = "Authentication failed" });
            }
            
            return Ok(new 
            { 
                success = true,
                message = "Authentication successful",
                cookieCount = cookies.Count
            });
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("captcha") || ex.Message.Contains("2FA"))
        {
            return BadRequest(new 
            { 
                error = "Authentication requires manual intervention",
                details = "Please use manual cookie extraction method for accounts with 2FA or captcha"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Browser authentication failed");
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpPost("refresh")]
    public async Task<IActionResult> RefreshFromSession(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Attempting to refresh cookies from browser session");
            
            var cookies = await _browserAuthService.GetCookiesFromSessionAsync(cancellationToken);
            
            if (cookies == null || cookies.Count == 0)
            {
                return NotFound(new { error = "No active session found" });
            }
            
            return Ok(new 
            { 
                success = true,
                message = "Cookies refreshed from session",
                cookieCount = cookies.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh cookies");
            return BadRequest(new { error = ex.Message });
        }
    }
}

public class SetCookiesRequest
{
    public string? Cookies { get; set; }
}

public class BrowserLoginRequest
{
    public string? Username { get; set; }
    public string? Password { get; set; }
}