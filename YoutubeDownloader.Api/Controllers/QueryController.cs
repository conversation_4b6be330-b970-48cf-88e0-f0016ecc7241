using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YoutubeDownloader.Api.Models.Requests;
using YoutubeDownloader.Api.Models.Responses;
using YoutubeDownloader.Api.Services;
using YoutubeDownloader.Core.Resolving;

namespace YoutubeDownloader.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class QueryController : ControllerBase
{
    private readonly IConfigurationService _configService;
    private readonly ILogger<QueryController> _logger;

    public QueryController(
        IConfigurationService configService,
        ILogger<QueryController> logger)
    {
        _configService = configService;
        _logger = logger;
    }

    [HttpPost("resolve")]
    public async Task<ActionResult<QueryResponse>> ResolveQuery(
        [FromBody] ResolveQueryRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Resolving query: {Query}", request.Query);
            
            var resolver = new QueryResolver(_configService.GetAuthCookies() ?? []);
            var result = await resolver.ResolveAsync(request.Query, cancellationToken);
            
            var response = new QueryResponse
            {
                Kind = result.Kind,
                Title = result.Title,
                Videos = result.Videos.Select(v => new VideoResponse
                {
                    Id = v.Id,
                    Title = v.Title,
                    Author = v.Author?.ChannelTitle,
                    Duration = v.Duration,
                    ThumbnailUrl = v.Thumbnails.OrderByDescending(t => t.Resolution.Area).FirstOrDefault()?.Url
                }).ToList()
            };
            
            _logger.LogInformation("Query resolved: {Kind} - {VideoCount} videos", result.Kind, result.Videos.Count);
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving query: {Query}", request.Query);
            return BadRequest(new { error = ex.Message });
        }
    }
}