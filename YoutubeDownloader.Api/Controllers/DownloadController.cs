using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YoutubeDownloader.Api.Models.Requests;
using YoutubeDownloader.Api.Models.Responses;
using YoutubeDownloader.Api.Services;

namespace YoutubeDownloader.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DownloadController : ControllerBase
{
    private readonly IDownloadService _downloadService;
    private readonly ILogger<DownloadController> _logger;

    public DownloadController(
        IDownloadService downloadService,
        ILogger<DownloadController> logger)
    {
        _downloadService = downloadService;
        _logger = logger;
    }

    [HttpGet("options/{videoId}")]
    public async Task<ActionResult<IReadOnlyList<DownloadOptionResponse>>> GetDownloadOptions(
        string videoId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting download options for video: {VideoId}", videoId);
            var options = await _downloadService.GetDownloadOptionsAsync(videoId, cancellationToken);
            return Ok(options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting download options for video: {VideoId}", videoId);
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpPost("start")]
    public async Task<ActionResult<object>> StartDownload(
        [FromBody] StartDownloadRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var identifier = request.Url ?? request.VideoId ?? "unknown";
            _logger.LogInformation("Starting download for: {Identifier}", identifier);
            
            var downloadId = await _downloadService.StartDownloadAsync(request, cancellationToken);
            
            if (request.WaitForCompletion)
            {
                // Download completed synchronously
                var status = await _downloadService.GetDownloadStatusAsync(downloadId);
                
                if (status?.Status == "Completed" && request.ReturnDownloadUrl)
                {
                    // Return direct download URL
                    var downloadUrl = $"{Request.Scheme}://{Request.Host}/api/download/file/{downloadId}";
                    return Ok(new 
                    { 
                        downloadId, 
                        status = status.Status,
                        downloadUrl,
                        fileName = status.FileName,
                        fileSizeBytes = status.FileSizeBytes
                    });
                }
                
                return Ok(new 
                { 
                    downloadId, 
                    status = status?.Status ?? "Failed",
                    fileName = status?.FileName,
                    fileSizeBytes = status?.FileSizeBytes,
                    error = status?.ErrorMessage
                });
            }
            else
            {
                // Queued for background processing
                return Ok(new { downloadId, status = "queued" });
            }
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request: {Message}", ex.Message);
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting download");
            return BadRequest(new { error = ex.Message });
        }
    }

    [HttpGet("status/{downloadId}")]
    public async Task<ActionResult<DownloadStatusResponse>> GetDownloadStatus(string downloadId)
    {
        var status = await _downloadService.GetDownloadStatusAsync(downloadId);
        if (status == null)
        {
            return NotFound(new { error = "Download not found" });
        }
        
        return Ok(status);
    }

    [HttpGet("list")]
    public async Task<ActionResult<IReadOnlyList<DownloadStatusResponse>>> GetAllDownloads()
    {
        var downloads = await _downloadService.GetAllDownloadsAsync();
        return Ok(downloads);
    }

    [HttpGet("file/{downloadId}")]
    public async Task<IActionResult> DownloadFile(string downloadId)
    {
        var result = await _downloadService.GetDownloadFileAsync(downloadId);
        if (!result.HasValue)
        {
            return NotFound(new { error = "File not found or download not completed" });
        }

        var (stream, fileName, contentType) = result.Value;
        return File(stream!, contentType!, fileName!);
    }

    [HttpDelete("{downloadId}")]
    public async Task<IActionResult> CancelDownload(string downloadId)
    {
        var cancelled = await _downloadService.CancelDownloadAsync(downloadId);
        if (!cancelled)
        {
            return NotFound(new { error = "Download not found" });
        }
        
        return Ok(new { message = "Download cancelled" });
    }
}