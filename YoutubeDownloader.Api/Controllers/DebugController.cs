using System;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YoutubeDownloader.Core.Utils;
using YoutubeExplode;

namespace YoutubeDownloader.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DebugController : ControllerBase
{
    private readonly ILogger<DebugController> _logger;
    
    public DebugController(ILogger<DebugController> logger)
    {
        _logger = logger;
    }
    
    [HttpGet("test-youtube")]
    public async Task<IActionResult> TestYouTube()
    {
        var results = new System.Collections.Generic.List<object>();
        
        // Test 1: Using shared Http.Client
        try
        {
            _logger.LogInformation("Testing with shared Http.Client");
            var youtube1 = new YoutubeClient(Http.Client);
            var video1 = await youtube1.Videos.GetAsync("dQw4w9WgXcQ");
            results.Add(new { test = "SharedHttpClient", success = true, title = video1.Title });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed with shared Http.Client");
            results.Add(new { test = "SharedHttpClient", success = false, error = ex.Message });
        }
        
        // Test 2: Using default YoutubeClient
        try
        {
            _logger.LogInformation("Testing with default YoutubeClient");
            var youtube2 = new YoutubeClient();
            var video2 = await youtube2.Videos.GetAsync("dQw4w9WgXcQ");
            results.Add(new { test = "DefaultClient", success = true, title = video2.Title });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed with default YoutubeClient");
            results.Add(new { test = "DefaultClient", success = false, error = ex.Message });
        }
        
        // Test 3: Using browser-like user agent
        try
        {
            _logger.LogInformation("Testing with browser user agent");
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            var youtube3 = new YoutubeClient(httpClient);
            var video3 = await youtube3.Videos.GetAsync("dQw4w9WgXcQ");
            results.Add(new { test = "BrowserUserAgent", success = true, title = video3.Title });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed with browser user agent");
            results.Add(new { test = "BrowserUserAgent", success = false, error = ex.Message });
        }
        
        // Test 4: Check Http.Client configuration
        results.Add(new 
        { 
            test = "HttpClientInfo",
            userAgent = Http.Client.DefaultRequestHeaders.UserAgent.ToString(),
            timeout = Http.Client.Timeout.TotalSeconds
        });
        
        return Ok(results);
    }
    
    [HttpGet("test-network")]
    public async Task<IActionResult> TestNetwork()
    {
        try
        {
            using var client = new HttpClient();
            var response = await client.GetAsync("https://www.youtube.com");
            return Ok(new 
            { 
                success = true, 
                statusCode = (int)response.StatusCode,
                headers = response.Headers.ToString()
            });
        }
        catch (Exception ex)
        {
            return Ok(new { success = false, error = ex.Message });
        }
    }
}