using System;
using System.IO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YoutubeDownloader.Api.Services;
using YoutubeDownloader.Core.Downloading;

namespace YoutubeDownloader.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly IStorageService _storageService;
    private readonly IDownloadQueueService _queueService;
    private readonly ILogger<HealthController> _logger;

    public HealthController(
        IStorageService storageService,
        IDownloadQueueService queueService,
        ILogger<HealthController> logger)
    {
        _storageService = storageService;
        _queueService = queueService;
        _logger = logger;
    }

    [HttpGet]
    public IActionResult GetHealth()
    {
        var health = new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "1.0.0",
            checks = new
            {
                ffmpeg = FFmpeg.IsAvailable(),
                downloadPath = Directory.Exists(_storageService.GetDownloadPath()),
                queueLength = _queueService.GetQueueLength()
            }
        };
        
        return Ok(health);
    }

    [HttpGet("ready")]
    public IActionResult GetReadiness()
    {
        if (!FFmpeg.IsAvailable())
        {
            _logger.LogWarning("FFmpeg is not available");
            return ServiceUnavailable(new { error = "FFmpeg is not available" });
        }

        if (!Directory.Exists(_storageService.GetDownloadPath()))
        {
            _logger.LogWarning("Download directory does not exist");
            return ServiceUnavailable(new { error = "Download directory is not accessible" });
        }

        return Ok(new { ready = true });
    }

    [HttpGet("live")]
    public IActionResult GetLiveness()
    {
        return Ok(new { alive = true });
    }

    private IActionResult ServiceUnavailable(object value)
    {
        return StatusCode(503, value);
    }
}