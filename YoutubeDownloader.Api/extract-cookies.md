# How to Extract YouTube Cookies for Authentication

Some YouTube videos are age-restricted or require sign-in to access. To download these videos, you need to provide authentication cookies from your browser.

## Method 1: Using Browser Developer Tools (Recommended)

1. **Open YouTube in your browser** and sign in to your account
2. **Open Developer Tools** (F12 or right-click → Inspect)
3. **Go to the Application/Storage tab**
4. **Select Cookies → https://www.youtube.com**
5. **Look for these important cookies:**
   - `__Secure-1PAPISID`
   - `__Secure-3PAPISID`
   - `__Secure-1PSID`
   - `__Secure-3PSID`
   - `SAPISID`
   - `APISID`
   - `HSID`
   - `SSID`
   - `SID`
   - `LOGIN_INFO` (if available)

6. **Copy the cookies** in one of these formats:

### Simple Format (name=value pairs)
```
SAPISID=value1; APISID=value2; HSID=value3; SSID=value4; SID=value5
```

### JSON Format
```json
[
  {
    "Name": "__Secure-1PAPISID",
    "Value": "your_cookie_value",
    "Domain": ".youtube.com",
    "Path": "/"
  },
  {
    "Name": "__Secure-3PAPISID",
    "Value": "your_cookie_value",
    "Domain": ".youtube.com",
    "Path": "/"
  }
]
```

## Method 2: Using Browser Extensions

You can use browser extensions like:
- **EditThisCookie** (Chrome/Edge)
- **Cookie Quick Manager** (Firefox)
- **Get cookies.txt** (Chrome/Firefox)

Export the cookies and format them as shown above.

## Method 3: Using yt-dlp (Alternative)

If you have yt-dlp installed, you can extract cookies:

```bash
# Export cookies from Chrome
yt-dlp --cookies-from-browser chrome --cookies cookies.txt --skip-download "https://www.youtube.com"

# Then read the cookies.txt file and format for the API
```

## Setting Cookies in the API

### Via API Endpoint
```bash
curl -X POST http://localhost:8080/api/auth/set-cookies \
  -H "Content-Type: application/json" \
  -d '{
    "cookies": "SAPISID=xxx; APISID=yyy; HSID=zzz"
  }'
```

### Via Environment Variable
```bash
# In docker-compose.yml or .env file
AUTH_COOKIES="SAPISID=xxx; APISID=yyy; HSID=zzz"
```

### Via Docker Run
```bash
docker run -p 8080:8080 \
  -e AUTH_COOKIES="SAPISID=xxx; APISID=yyy" \
  youtube-downloader:latest
```

## Testing Authentication

1. **Check auth status:**
```bash
curl http://localhost:8080/api/auth/status
```

2. **Try downloading a restricted video:**
```bash
curl -X POST http://localhost:8080/api/download/start \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.youtube.com/watch?v=RESTRICTED_VIDEO_ID"
  }'
```

## Important Notes

- **Cookie Expiration**: YouTube cookies expire after some time. You may need to refresh them periodically.
- **Security**: Keep your cookies private. They provide access to your YouTube account.
- **Account Safety**: Using cookies for downloading may violate YouTube's terms of service. Use at your own risk.
- **Region Restrictions**: Cookies don't bypass region restrictions, only age/sign-in restrictions.

## Troubleshooting

If videos still show as unavailable:
1. Make sure you're logged into YouTube when copying cookies
2. Verify the cookies haven't expired
3. Try copying ALL cookies from youtube.com, not just the secure ones
4. Check that the video is actually accessible in your browser when logged in