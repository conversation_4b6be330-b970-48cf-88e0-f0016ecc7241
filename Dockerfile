# Multi-stage build for YoutubeDownloader HTTP API
# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy solution and project files first for better caching
COPY *.sln ./
COPY Directory.Build.props ./
COPY NuGet.config ./
COPY YoutubeDownloader.Core/*.csproj ./YoutubeDownloader.Core/
COPY YoutubeDownloader.Api/*.csproj ./YoutubeDownloader.Api/

# Restore dependencies
RUN dotnet restore YoutubeDownloader.Api/YoutubeDownloader.Api.csproj

# Copy source code
COPY YoutubeDownloader.Core/ ./YoutubeDownloader.Core/
COPY YoutubeDownloader.Api/ ./YoutubeDownloader.Api/

# Build and publish the application
WORKDIR /src/YoutubeDownloader.Api
RUN dotnet publish -c Release -o /app/publish \
    --no-restore \
    --self-contained false \
    /p:PublishSingleFile=false \
    /p:PublishTrimmed=false

# Stage 2: Runtime image - Alpine Linux for smaller size
FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine
WORKDIR /app

# Install FFmpeg and basic utilities
RUN apk add --no-cache \
    ffmpeg \
    curl \
    icu-libs \
    icu-data-full \
    ca-certificates

# Create directories for downloads, config, logs, and temp
RUN mkdir -p /app/downloads /app/config /app/logs /app/temp

# Copy published application from build stage
COPY --from=build /app/publish .

# Create non-root user for security (Alpine uses adduser)
RUN adduser -D -u 1000 -s /bin/sh appuser && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Default environment variables for production
ENV ASPNETCORE_ENVIRONMENT=Production \
    ASPNETCORE_URLS=http://+:8080 \
    LC_ALL=C.UTF-8 \
    LANG=C.UTF-8 \
    TZ=UTC \
    DOWNLOAD_PATH=/app/downloads \
    TEMP_PATH=/app/temp \
    MAX_PARALLEL_DOWNLOADS=2 \
    DEFAULT_QUALITY=Highest \
    DEFAULT_FORMAT=mp4 \
    FFMPEG_PATH=/usr/bin/ffmpeg \
    ENABLE_SUBTITLES=true \
    ENABLE_TAGS=true \
    INCLUDE_LANGUAGE_SPECIFIC_AUDIO=true \
    SKIP_EXISTING_FILES=false \
    FILE_NAME_TEMPLATE='$title' \
    CLEANUP_ENABLED=true \
    CLEANUP_INTERVAL_HOURS=24 \
    MAX_FILE_RETENTION_DAYS=7 \
    EnableSwagger=false \
    Logging__LogLevel__Default=Information \
    Logging__LogLevel__Microsoft.AspNetCore=Warning

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/api/health/live || exit 1

# Expose port
EXPOSE 8080

# Volumes for persistent data
VOLUME ["/app/downloads", "/app/config", "/app/logs", "/app/temp"]

# Entry point
ENTRYPOINT ["dotnet", "YoutubeDownloader.Api.dll"]