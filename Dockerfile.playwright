# Multi-stage build for YoutubeDownloader HTTP API with Playwright support
# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy solution and project files first for better caching
COPY *.sln ./
COPY Directory.Build.props ./
COPY NuGet.config ./
COPY YoutubeDownloader.Core/*.csproj ./YoutubeDownloader.Core/
COPY YoutubeDownloader.Api/*.csproj ./YoutubeDownloader.Api/

# Restore dependencies
RUN dotnet restore YoutubeDownloader.Api/YoutubeDownloader.Api.csproj

# Copy source code
COPY YoutubeDownloader.Core/ ./YoutubeDownloader.Core/
COPY YoutubeDownloader.Api/ ./YoutubeDownloader.Api/

# Build and publish the application
WORKDIR /src/YoutubeDownloader.Api
RUN dotnet publish -c Release -o /app/publish \
    --no-restore \
    --self-contained false \
    /p:PublishSingleFile=false \
    /p:PublishTrimmed=false

# Stage 2: Playwright preparation
FROM mcr.microsoft.com/playwright/dotnet:v1.48.0-noble AS playwright-deps

# Stage 3: Final runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0
WORKDIR /app

# Install FFmpeg and essential tools
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Copy Playwright dependencies from playwright image
COPY --from=playwright-deps /ms-playwright /ms-playwright
COPY --from=playwright-deps /usr/lib/x86_64-linux-gnu /usr/lib/x86_64-linux-gnu
COPY --from=playwright-deps /usr/share/fonts /usr/share/fonts
COPY --from=playwright-deps /etc/fonts /etc/fonts

# Install additional required libraries
RUN apt-get update && apt-get install -y \
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libx11-6 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libxcb1 \
    libxss1 \
    libgtk-3-0 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Create application directories
RUN mkdir -p /app/downloads /app/config /app/logs /app/temp /app/browser-session

# Copy published application from build stage
COPY --from=build /app/publish .

# Set Playwright environment
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# Create non-root user for security
RUN useradd -m -u 1000 -s /bin/bash appuser && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /ms-playwright

# Switch to non-root user
USER appuser

# Default environment variables for production
ENV ASPNETCORE_ENVIRONMENT=Production \
    ASPNETCORE_URLS=http://+:8080 \
    LC_ALL=C.UTF-8 \
    LANG=C.UTF-8 \
    TZ=UTC \
    DOWNLOAD_PATH=/app/downloads \
    TEMP_PATH=/app/temp \
    MAX_PARALLEL_DOWNLOADS=2 \
    DEFAULT_QUALITY=Highest \
    DEFAULT_FORMAT=mp4 \
    FFMPEG_PATH=/usr/bin/ffmpeg \
    ENABLE_SUBTITLES=true \
    ENABLE_TAGS=true \
    INCLUDE_LANGUAGE_SPECIFIC_AUDIO=true \
    SKIP_EXISTING_FILES=false \
    FILE_NAME_TEMPLATE='$title' \
    CLEANUP_ENABLED=true \
    CLEANUP_INTERVAL_HOURS=24 \
    MAX_FILE_RETENTION_DAYS=7 \
    EnableSwagger=false \
    Logging__LogLevel__Default=Information \
    Logging__LogLevel__Microsoft.AspNetCore=Warning \
    BROWSER_HEADLESS=true \
    BROWSER_SESSION_PATH=/app/browser-session

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/api/health/live || exit 1

# Expose port
EXPOSE 8080

# Volumes for persistent data
VOLUME ["/app/downloads", "/app/config", "/app/logs", "/app/temp", "/app/browser-session"]

# Entry point
ENTRYPOINT ["dotnet", "YoutubeDownloader.Api.dll"]