# Multi-stage build for YoutubeDownloader HTTP API with full browser support
# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy solution and project files first for better caching
COPY *.sln ./
COPY Directory.Build.props ./
COPY NuGet.config ./
COPY YoutubeDownloader.Core/*.csproj ./YoutubeDownloader.Core/
COPY YoutubeDownloader.Api/*.csproj ./YoutubeDownloader.Api/

# Restore dependencies
RUN dotnet restore YoutubeDownloader.Api/YoutubeDownloader.Api.csproj

# Copy source code
COPY YoutubeDownloader.Core/ ./YoutubeDownloader.Core/
COPY YoutubeDownloader.Api/ ./YoutubeDownloader.Api/

# Build and publish the application
WORKDIR /src/YoutubeDownloader.Api
RUN dotnet publish -c Release -o /app/publish \
    --no-restore \
    --self-contained false \
    /p:PublishSingleFile=false \
    /p:PublishTrimmed=false

# Stage 2: Runtime image with <PERSON><PERSON> pre-installed
FROM mcr.microsoft.com/playwright/dotnet:v1.48.0-noble AS runtime
WORKDIR /app

# Install FFmpeg and other required tools
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy published application from build stage
COPY --from=build /app/publish .

# Create directories for persistent data
RUN mkdir -p /app/downloads /app/config /app/logs /app/temp /app/browser-data

# Create a startup script that handles authentication
RUN echo '#!/bin/bash' > /app/startup.sh && \
    echo 'echo "==========================================="' >> /app/startup.sh && \
    echo 'echo "YoutubeDownloader API Starting..."' >> /app/startup.sh && \
    echo 'echo "==========================================="' >> /app/startup.sh && \
    echo '' >> /app/startup.sh && \
    echo '# Check if we have stored cookies' >> /app/startup.sh && \
    echo 'if [ -f "/app/config/cookies.json" ]; then' >> /app/startup.sh && \
    echo '    echo "Found stored cookies, loading..."' >> /app/startup.sh && \
    echo '    export AUTH_COOKIES=$(cat /app/config/cookies.json)' >> /app/startup.sh && \
    echo 'else' >> /app/startup.sh && \
    echo '    echo ""' >> /app/startup.sh && \
    echo '    echo "No stored cookies found"' >> /app/startup.sh && \
    echo '    echo "The API will attempt to obtain cookies automatically"' >> /app/startup.sh && \
    echo '    echo ""' >> /app/startup.sh && \
    echo '    if [ ! -z "$YOUTUBE_USERNAME" ] && [ ! -z "$YOUTUBE_PASSWORD" ]; then' >> /app/startup.sh && \
    echo '        echo "Credentials provided - will attempt full authentication"' >> /app/startup.sh && \
    echo '    else' >> /app/startup.sh && \
    echo '        echo "No credentials - will try to get basic cookies"' >> /app/startup.sh && \
    echo '        echo "For full access, set YOUTUBE_USERNAME and YOUTUBE_PASSWORD"' >> /app/startup.sh && \
    echo '    fi' >> /app/startup.sh && \
    echo 'fi' >> /app/startup.sh && \
    echo '' >> /app/startup.sh && \
    echo 'echo "Starting API on port 8080..."' >> /app/startup.sh && \
    echo 'exec dotnet YoutubeDownloader.Api.dll' >> /app/startup.sh

RUN chmod +x /app/startup.sh

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production \
    ASPNETCORE_URLS=http://+:8080 \
    LC_ALL=C.UTF-8 \
    LANG=C.UTF-8 \
    TZ=UTC \
    DOWNLOAD_PATH=/app/downloads \
    TEMP_PATH=/app/temp \
    CONFIG_PATH=/app/config \
    MAX_PARALLEL_DOWNLOADS=2 \
    DEFAULT_QUALITY=Highest \
    DEFAULT_FORMAT=mp4 \
    FFMPEG_PATH=/usr/bin/ffmpeg \
    ENABLE_SUBTITLES=true \
    ENABLE_TAGS=true \
    INCLUDE_LANGUAGE_SPECIFIC_AUDIO=true \
    SKIP_EXISTING_FILES=false \
    FILE_NAME_TEMPLATE='$title' \
    CLEANUP_ENABLED=true \
    CLEANUP_INTERVAL_HOURS=24 \
    MAX_FILE_RETENTION_DAYS=7 \
    EnableSwagger=true \
    Logging__LogLevel__Default=Information \
    Logging__LogLevel__Microsoft.AspNetCore=Warning \
    BROWSER_HEADLESS=true \
    BROWSER_DATA_PATH=/app/browser-data \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    ENABLE_BROWSER_AUTH=true

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/api/health/live || exit 1

# Expose port
EXPOSE 8080

# Volumes for persistent data
VOLUME ["/app/downloads", "/app/config", "/app/logs", "/app/browser-data"]

# Entry point
ENTRYPOINT ["/app/startup.sh"]