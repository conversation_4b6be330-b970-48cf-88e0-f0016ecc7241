# YoutubeDownloader - Desktop to HTTP API Migration Plan

## Project Overview
YoutubeDownloader is a .NET 9.0 desktop application built with Avalonia UI that allows users to download YouTube videos with various quality options, format selections, and metadata tagging.

## Current Architecture

### Technology Stack
- **Framework**: .NET 9.0
- **UI Framework**: Avalonia 11.3.0
- **Core Libraries**:
  - YoutubeExplode 6.5.4 (YouTube API interaction)
  - YoutubeExplode.Converter 6.5.4 (Video conversion)
  - FFmpeg (Video processing)
  - TagLibSharp 2.3.0 (Media tagging)
  - Gress 2.1.1 (Progress reporting)

### Project Structure
- **YoutubeDownloader.Core**: Core business logic (downloading, resolving, tagging)
- **YoutubeDownloader**: Desktop UI application (Views, ViewModels, Services)

### Key Features
1. **Query Resolution**: Support for videos, playlists, channels, and search
2. **Download Options**: Multiple quality preferences and container formats
3. **Authentication**: Cookie-based authentication support
4. **Parallel Downloads**: Configurable concurrent download limits
5. **Media Tagging**: Automatic metadata injection
6. **Subtitle Support**: Download and embed subtitles
7. **Progress Tracking**: Real-time download progress monitoring

## Migration Plan

### Phase 1: Create HTTP API Project

#### 1.1 New Project Structure
```
YoutubeDownloader.Api/
├── Controllers/
│   ├── DownloadController.cs
│   ├── QueryController.cs
│   └── HealthController.cs
├── Models/
│   ├── Requests/
│   └── Responses/
├── Services/
│   ├── DownloadService.cs
│   ├── DownloadQueueService.cs
│   └── ProgressHubService.cs
├── Middleware/
│   └── ErrorHandlingMiddleware.cs
├── Program.cs
├── appsettings.json
└── YoutubeDownloader.Api.csproj
```

#### 1.2 API Endpoints Design

```http
# Query Resolution
POST /api/query/resolve
Body: { "query": "youtube_url_or_search" }
Response: { "kind": "video|playlist|channel", "videos": [...] }

# Get Download Options
GET /api/download/options/{videoId}
Response: { "options": [{ "quality": "...", "format": "...", "size": "..." }] }

# Start Download
POST /api/download/start
Body: { 
  "videoId": "...", 
  "quality": "1080p60", 
  "format": "mp4",
  "includeSubtitles": true,
  "injectTags": true 
}
Response: { "downloadId": "guid", "status": "queued" }

# Get Download Status
GET /api/download/status/{downloadId}
Response: { "status": "queued|downloading|completed|failed", "progress": 0.75 }

# List Downloads
GET /api/download/list
Response: [{ "id": "...", "video": {...}, "status": "...", "progress": ... }]

# Download File
GET /api/download/file/{downloadId}
Response: Binary file stream

# Cancel Download
DELETE /api/download/{downloadId}

# Health Check
GET /api/health
```

### Phase 2: Core Services Implementation

#### 2.1 Download Service
- Reuse `VideoDownloader` from Core project
- Implement download queue management
- Handle file storage and retrieval
- Progress tracking via SignalR/WebSockets

#### 2.2 Configuration Service
- Migrate settings from desktop SettingsService
- Use appsettings.json and environment variables
- Support for:
  - Download directory path
  - Parallel download limit
  - Default quality preferences
  - FFmpeg path configuration
  - Authentication cookies

#### 2.3 Background Services
- Implement `IHostedService` for queue processing
- Cleanup service for old/incomplete downloads
- Progress notification service

### Phase 3: Docker Containerization

#### 3.1 Dockerfile Structure
```dockerfile
# Multi-stage build
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime

# Include FFmpeg
RUN apt-get update && apt-get install -y ffmpeg

# Copy and run application
```

#### 3.2 Docker Compose
```yaml
services:
  api:
    build: .
    ports:
      - "5000:8080"
    volumes:
      - ./downloads:/app/downloads
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
```

### Phase 4: Implementation Steps

1. **Create new ASP.NET Core Web API project**
   - Target .NET 9.0
   - Add reference to YoutubeDownloader.Core
   - Configure dependency injection

2. **Implement API Controllers**
   - QueryController for URL resolution
   - DownloadController for download management
   - Implement request/response models

3. **Add Background Services**
   - Download queue processor
   - Progress tracking service
   - File cleanup service

4. **Implement Storage**
   - Local file system for downloads
   - In-memory or Redis for queue/status

5. **Add Real-time Updates**
   - SignalR hub for progress updates
   - WebSocket alternative for simpler clients

6. **Security & Configuration**
   - CORS configuration
   - Rate limiting
   - API key authentication (optional)
   - Configurable download limits

7. **Testing & Documentation**
   - Unit tests for services
   - Integration tests for API
   - OpenAPI/Swagger documentation

### Phase 5: Deployment Considerations

#### Environment Variables
```
DOWNLOAD_PATH=/app/downloads
MAX_PARALLEL_DOWNLOADS=2
DEFAULT_QUALITY=Highest
FFMPEG_PATH=/usr/bin/ffmpeg
ENABLE_SUBTITLES=true
ENABLE_TAGS=true
CLEANUP_INTERVAL_HOURS=24
MAX_FILE_RETENTION_DAYS=7
```

#### Volume Mounts
- `/app/downloads`: Download storage
- `/app/config`: Configuration files
- `/app/logs`: Application logs

#### Health Checks
- Liveness: API responsiveness
- Readiness: FFmpeg availability, disk space

## Dependencies to Add

### NuGet Packages for API Project
```xml
<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.*" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="6.*" />
<PackageReference Include="Microsoft.AspNetCore.SignalR" Version="9.0.*" />
<PackageReference Include="Serilog.AspNetCore" Version="8.*" />
```

## Migration Timeline

1. **Week 1**: Create API project structure, implement basic endpoints
2. **Week 2**: Integrate core downloading logic, add queue management
3. **Week 3**: Implement progress tracking, file management
4. **Week 4**: Dockerize application, testing, documentation

## Breaking Changes

### Removed Features
- Desktop UI components
- Local settings persistence
- Auto-update functionality
- Native OS integration

### API-First Benefits
- Platform agnostic
- Scalable architecture
- Multiple client support
- Container-ready deployment
- RESTful interface

## Testing Strategy

### Unit Tests
- Service layer logic
- Queue management
- File operations

### Integration Tests
- API endpoint validation
- Download workflow
- Error handling

### Load Tests
- Concurrent download limits
- Queue processing performance
- API throughput

## Security Considerations

1. **Input Validation**: Sanitize YouTube URLs and queries
2. **Rate Limiting**: Prevent abuse of download endpoints
3. **File Access**: Secure file download endpoints
4. **Storage Limits**: Implement quota management
5. **CORS Policy**: Configure allowed origins

## Monitoring & Logging

- Structured logging with Serilog
- Application metrics (downloads, errors, queue size)
- Health endpoint for monitoring
- Performance counters

## Future Enhancements

1. **Database Integration**: Store download history and metadata
2. **User Management**: Multi-user support with quotas
3. **Cloud Storage**: S3/Azure Blob integration
4. **Webhook Support**: Notify external systems on completion
5. **Batch Operations**: Multiple video downloads in single request
6. **Format Conversion**: Post-download processing options