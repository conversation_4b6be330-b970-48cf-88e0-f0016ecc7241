# Docker Deployment Guide

This guide explains how to deploy the YoutubeDownloader API using Docker and Docker Compose.

## Prerequisites

- Docker Engine 20.10+ 
- Docker Compose 2.0+
- 2GB+ RAM available
- 10GB+ disk space for downloads

## Quick Start

### 1. Development Mode

Run the API with development settings and Swagger enabled:

```bash
# Build and start the service
docker-compose up --build

# API will be available at http://localhost:5000
# Swagger UI at http://localhost:5000/swagger
```

### 2. Production Mode

Run the API with production optimizations:

```bash
# Using production configuration
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# API will be available at http://localhost:8080
# Nginx proxy at http://localhost:80
```

### 3. Build Only

Build the Docker image without running:

```bash
docker build -t youtube-downloader:latest .
```

## Configuration

### Environment Variables

All configuration is done through environment variables. Key settings:

| Variable | Default | Description |
|----------|---------|-------------|
| `DOWNLOAD_PATH` | `/data/downloads` | Where downloaded files are stored |
| `MAX_PARALLEL_DOWNLOADS` | `2` | Number of concurrent downloads |
| `DEFAULT_FORMAT` | `mp4` | Default output format |
| `DEFAULT_QUALITY` | `Highest` | Default video quality |
| `CLEANUP_ENABLED` | `true` | Enable automatic file cleanup |
| `MAX_FILE_RETENTION_DAYS` | `7` | Days to keep downloaded files |
| `EnableSwagger` | `false` | Enable Swagger UI (dev only) |
| `AUTH_COOKIES` | `""` | YouTube auth cookies for restricted videos |

### Volume Mounts

The application uses several volumes for persistent data:

- `/data/downloads` - Downloaded video files
- `/data/logs` - Application logs
- `/data/config` - Configuration files
- `/data/temp` - Temporary files during processing

## Deployment Scenarios

### Standalone API

Basic deployment without reverse proxy:

```bash
docker run -d \
  --name youtube-downloader \
  -p 8080:8080 \
  -v $(pwd)/downloads:/data/downloads \
  -e MAX_PARALLEL_DOWNLOADS=3 \
  youtube-downloader:latest
```

### With Nginx Reverse Proxy

Full production setup with Nginx:

```bash
# Start both API and Nginx
docker-compose -f docker-compose.yml up -d

# Or with production settings
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### With Custom Settings

Create a `.env` file for custom settings:

```env
# .env
MAX_PARALLEL_DOWNLOADS=5
DEFAULT_QUALITY=UpTo1080p
DEFAULT_FORMAT=mp3
CLEANUP_INTERVAL_HOURS=6
MAX_FILE_RETENTION_DAYS=3
```

Then run:

```bash
docker-compose --env-file .env up -d
```

### Scaling

Scale the API service (requires load balancer):

```bash
docker-compose up -d --scale youtube-downloader-api=3
```

## Docker Compose Files

### File Structure

- `docker-compose.yml` - Base configuration
- `docker-compose.override.yml` - Development overrides (auto-loaded)
- `docker-compose.prod.yml` - Production configuration
- `.env` - Environment variables (optional)

### Development vs Production

**Development** (`docker-compose.override.yml`):
- Swagger UI enabled
- Debug logging
- Local volume mounts
- Port 5000

**Production** (`docker-compose.prod.yml`):
- Optimized settings
- Nginx reverse proxy
- Named volumes
- Health checks
- Resource limits

## Management Commands

### View Logs

```bash
# All services
docker-compose logs -f

# API only
docker-compose logs -f youtube-downloader-api

# Last 100 lines
docker-compose logs --tail=100 youtube-downloader-api
```

### Health Check

```bash
# Check API health
curl http://localhost:8080/api/health

# Check readiness
curl http://localhost:8080/api/health/ready

# Via Docker
docker exec youtube-downloader-api curl http://localhost:8080/api/health
```

### Restart Services

```bash
# Restart all services
docker-compose restart

# Restart API only
docker-compose restart youtube-downloader-api
```

### Update Application

```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Backup Downloads

```bash
# Backup downloads volume
docker run --rm \
  -v youtube-downloads:/data \
  -v $(pwd)/backup:/backup \
  alpine tar czf /backup/downloads-$(date +%Y%m%d).tar.gz -C /data .
```

## Monitoring

### Container Stats

```bash
# Real-time stats
docker stats youtube-downloader-api

# One-time snapshot
docker stats --no-stream
```

### Check Downloads

```bash
# List downloads
docker exec youtube-downloader-api ls -la /data/downloads

# Check disk usage
docker exec youtube-downloader-api df -h /data/downloads
```

### API Metrics

Access the health endpoint for basic metrics:

```bash
curl http://localhost:8080/api/health | jq
```

## Troubleshooting

### Common Issues

#### 1. FFmpeg Not Found

The Docker image includes FFmpeg. If you see FFmpeg errors:

```bash
# Check FFmpeg installation
docker exec youtube-downloader-api ffmpeg -version
```

#### 2. Permission Denied

Fix volume permissions:

```bash
# Set correct permissions
sudo chown -R 1000:1000 ./downloads
sudo chmod -R 755 ./downloads
```

#### 3. Out of Disk Space

Check and clean up:

```bash
# Check disk usage
docker system df

# Clean unused data
docker system prune -a

# Remove old downloads
docker exec youtube-downloader-api \
  find /data/downloads -type f -mtime +7 -delete
```

#### 4. Container Won't Start

Check logs:

```bash
# View startup logs
docker-compose logs youtube-downloader-api

# Check container status
docker-compose ps
```

### Debug Mode

Enable debug logging:

```bash
docker-compose down
docker-compose -e Logging__LogLevel__Default=Debug up
```

## Security Considerations

### Production Checklist

- [ ] Disable Swagger UI (`EnableSwagger=false`)
- [ ] Configure HTTPS with SSL certificates
- [ ] Set up firewall rules
- [ ] Implement rate limiting
- [ ] Use named volumes instead of bind mounts
- [ ] Run as non-root user (already configured)
- [ ] Set resource limits
- [ ] Configure log rotation
- [ ] Regular security updates

### SSL/TLS Setup

1. Obtain SSL certificates (e.g., Let's Encrypt)
2. Place certificates in `nginx/ssl/`
3. Uncomment SSL configuration in `nginx/conf.d/youtube-downloader.conf`
4. Update docker-compose to expose port 443

## Performance Tuning

### API Settings

```yaml
environment:
  - MAX_PARALLEL_DOWNLOADS=5  # Increase for more concurrent downloads
  - CLEANUP_INTERVAL_HOURS=6  # Reduce for more frequent cleanup
```

### Docker Resources

```yaml
deploy:
  resources:
    limits:
      cpus: '4'
      memory: 4G
```

### Nginx Optimization

Edit `nginx/nginx.conf` for:
- Worker processes
- Connection limits
- Caching settings
- Compression levels

## Maintenance

### Regular Tasks

1. **Daily**: Check logs for errors
2. **Weekly**: Review disk usage
3. **Monthly**: Update base images
4. **Quarterly**: Security audit

### Automated Cleanup

The API automatically cleans old files based on:
- `CLEANUP_ENABLED` - Enable/disable cleanup
- `CLEANUP_INTERVAL_HOURS` - How often to run cleanup
- `MAX_FILE_RETENTION_DAYS` - How long to keep files

## Support

For issues or questions:
1. Check logs: `docker-compose logs`
2. Review health status: `/api/health`
3. Consult the main README.md
4. Check Docker documentation