# Development overrides - automatically loaded when running docker-compose up
version: '3.8'

services:
  youtube-downloader-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
      args:
        - BUILDKIT_PROGRESS=plain
    environment:
      # Development settings
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      
      # Enable detailed logging for development
      - Logging__LogLevel__Default=Debug
      - Logging__LogLevel__Microsoft.AspNetCore=Information
      - Serilog__MinimumLevel__Default=Debug
      
      # Enable Swagger UI for development
      - EnableSwagger=true
      
      # Faster cleanup for testing
      - CLEANUP_INTERVAL_HOURS=1
      - MAX_FILE_RETENTION_DAYS=1
      
      # Development paths (use local directories)
      - DOWNLOAD_PATH=/data/downloads
      - TEMP_PATH=/data/temp
      
    volumes:
      # Mount source code for hot reload (if using dotnet watch)
      # - ./YoutubeDownloader.Api:/app:ro
      # - ./YoutubeDownloader.Core:/app/YoutubeDownloader.Core:ro
      
      # Use local directories for easier access during development
      - ./dev-downloads:/data/downloads
      - ./dev-logs:/data/logs
      - ./dev-config:/data/config
      - ./dev-temp:/data/temp
      
    ports:
      - "5000:8080"  # Different port for development
      
    # Override resource limits for development
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 1G