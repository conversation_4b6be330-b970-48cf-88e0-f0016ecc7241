version: '3.8'

services:
  youtube-downloader-api:
    build:
      context: .
      dockerfile: Dockerfile  # Change to Dockerfile.full for browser auth support
    container_name: youtube-downloader-api
    image: youtube-downloader:latest
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Production settings
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      
      # Download settings
      - DOWNLOAD_PATH=/data/downloads
      - TEMP_PATH=/data/temp
      - MAX_PARALLEL_DOWNLOADS=3
      - DEFAULT_QUALITY=Highest
      - DEFAULT_FORMAT=mp4
      
      # Features
      - ENABLE_SUBTITLES=true
      - ENABLE_TAGS=true
      - INCLUDE_LANGUAGE_SPECIFIC_AUDIO=true
      - SKIP_EXISTING_FILES=false
      - FILE_NAME_TEMPLATE=$$title
      
      # Cleanup settings
      - CLEANUP_ENABLED=true
      - CLEANUP_INTERVAL_HOURS=12
      - MAX_FILE_RETENTION_DAYS=7
      
      # API settings
      - EnableSwagger=false
      
      # Logging
      - Logging__LogLevel__Default=Information
      - Logging__LogLevel__Microsoft.AspNetCore=Warning
      - Serilog__MinimumLevel__Default=Information
      
    volumes:
      # Persistent data volumes
      - youtube-downloads:/data/downloads
      - youtube-logs:/data/logs
      - youtube-config:/data/config
      - youtube-temp:/data/temp
      
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
      
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        
    networks:
      - youtube-network
      
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: youtube-downloader-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - youtube-downloads:/usr/share/nginx/html/downloads:ro
    depends_on:
      - youtube-downloader-api
    networks:
      - youtube-network
    profiles:
      - with-proxy

volumes:
  youtube-downloads:
    driver: local
  youtube-logs:
    driver: local
  youtube-config:
    driver: local
  youtube-temp:
    driver: local

networks:
  youtube-network:
    driver: bridge